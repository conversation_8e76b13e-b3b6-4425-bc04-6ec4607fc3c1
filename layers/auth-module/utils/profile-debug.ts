/**
 * Debug utilities for profile operations
 */

import { doc, getDoc } from 'firebase/firestore'
import { useAuth } from '../composables/auth'
import { useFirebase } from '../composables/firebase'

export async function debugProfileUpdate() {
  const { firestore } = useFirebase()
  const { user, currentWorkspace } = useAuth()

  console.warn('🔍 Profile Update Debug Info')

  try {
    // Check auth state
    console.warn('Auth State:', {
      userId: user.value?.id,
      workspaceId: currentWorkspace.value?.id,
      isAuthenticated: !!user.value,
      hasWorkspace: !!currentWorkspace.value,
    })

    if (!user.value?.id || !currentWorkspace.value?.id) {
      console.error('❌ Missing user or workspace')
      return
    }

    // Check if profile exists
    const profileId = `${user.value.id}_${currentWorkspace.value.id}`
    const profileRef = doc(firestore, 'profiles', profileId)
    const profileSnap = await getDoc(profileRef)

    console.warn('Profile Document:', {
      id: profileId,
      exists: profileSnap.exists(),
      data: profileSnap.exists() ? profileSnap.data() : null,
    })

    if (profileSnap.exists()) {
      const data = profileSnap.data()
      console.warn('Profile Fields:', {
        hasUserId: 'userId' in data,
        userId: data.userId,
        userIdMatches: data.userId === user.value.id,
        hasWorkspaceId: 'workspaceId' in data,
        workspaceId: data.workspaceId,
        workspaceIdMatches: data.workspaceId === currentWorkspace.value.id,
      })
    }

    // Check Firebase auth
    const { auth } = useFirebase()
    console.warn('Firebase Auth:', {
      currentUser: auth.currentUser?.uid,
      isSignedIn: !!auth.currentUser,
      matchesAppUser: auth.currentUser?.uid === user.value.id,
    })
  }
  catch (error: any) {
    console.error('Debug Error:', error)
  }
  finally {
    console.warn('End Profile Update Debug')
  }
}

export function logProfileUpdateAttempt(data: any) {
  console.warn('📝 Profile Update Attempt')
  console.warn('Update Data:', data)
  console.warn('Data Keys:', Object.keys(data))
  console.warn('Has Required Fields:', {
    displayName: 'displayName' in data,
    firstName: 'firstName' in data,
    lastName: 'lastName' in data,
  })
  console.warn('End Profile Update Attempt')
}
