import crypto from 'node:crypto'

// Encryption algorithm and key configuration
const ALGORITHM = 'aes-256-gcm'
const SALT_LENGTH = 32
const IV_LENGTH = 16
const TAG_LENGTH = 16
const KEY_LENGTH = 32
const ITERATIONS = 100000

/**
 * Derives an encryption key from a master key and salt
 */
function deriveKey(masterKey: string, salt: Buffer): Buffer {
  return crypto.pbkdf2Sync(masterKey, salt, ITERATIONS, KEY_LENGTH, 'sha256')
}

/**
 * Encrypts a string value using AES-256-GCM
 * @param value - The plaintext value to encrypt
 * @param masterKey - The master encryption key
 * @returns Base64 encoded encrypted data with salt, iv, and tag
 */
export function encrypt(value: string, masterKey: string): string {
  try {
    // Generate random salt and IV
    const salt = crypto.randomBytes(SALT_LENGTH)
    const iv = crypto.randomBytes(IV_LENGTH)

    // Derive key from master key and salt
    const key = deriveKey(masterKey, salt)

    // Create cipher
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv)

    // Encrypt the value
    const encrypted = Buffer.concat([
      cipher.update(value, 'utf8'),
      cipher.final(),
    ])

    // Get the authentication tag
    const tag = cipher.getAuthTag()

    // Combine salt, iv, tag, and encrypted data
    const combined = Buffer.concat([salt, iv, tag, encrypted])

    // Return base64 encoded result
    return combined.toString('base64')
  }
  catch (error) {
    throw new Error(`Encryption failed: ${(error as Error).message}`)
  }
}

/**
 * Decrypts a base64 encoded encrypted string
 * @param encryptedValue - Base64 encoded encrypted data
 * @param masterKey - The master encryption key
 * @returns The decrypted plaintext value
 */
export function decrypt(encryptedValue: string, masterKey: string): string {
  try {
    // Decode from base64
    const combined = Buffer.from(encryptedValue, 'base64')

    // Extract components
    const salt = combined.slice(0, SALT_LENGTH)
    const iv = combined.slice(SALT_LENGTH, SALT_LENGTH + IV_LENGTH)
    const tag = combined.slice(SALT_LENGTH + IV_LENGTH, SALT_LENGTH + IV_LENGTH + TAG_LENGTH)
    const encrypted = combined.slice(SALT_LENGTH + IV_LENGTH + TAG_LENGTH)

    // Derive key from master key and salt
    const key = deriveKey(masterKey, salt)

    // Create decipher
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv)
    decipher.setAuthTag(tag)

    // Decrypt the value
    const decrypted = Buffer.concat([
      decipher.update(encrypted),
      decipher.final(),
    ])

    return decrypted.toString('utf8')
  }
  catch (error) {
    throw new Error(`Decryption failed: ${(error as Error).message}`)
  }
}

/**
 * Masks an API key for display (shows first 4 and last 4 characters)
 * @param apiKey - The API key to mask
 * @returns Masked API key
 */
export function maskApiKey(apiKey: string): string {
  if (!apiKey || apiKey.length < 12) {
    return '••••••••'
  }

  const firstChars = apiKey.slice(0, 4)
  const lastChars = apiKey.slice(-4)
  const maskedLength = Math.max(8, apiKey.length - 8)
  const masked = '•'.repeat(maskedLength)

  return `${firstChars}${masked}${lastChars}`
}

/**
 * Validates API key format for different providers
 * @param apiKey - The API key to validate
 * @param provider - The provider type
 * @returns True if valid format, false otherwise
 */
export function validateApiKeyFormat(apiKey: string, provider: string): boolean {
  if (!apiKey || typeof apiKey !== 'string') {
    return false
  }

  // Remove any whitespace
  const cleanKey = apiKey.trim()

  switch (provider) {
    case 'openai':
      // OpenAI keys start with 'sk-' and are typically 51 characters
      return /^sk-[a-zA-Z0-9]{48}$/.test(cleanKey)

    case 'anthropic':
      // Anthropic keys start with 'sk-ant-' followed by alphanumeric characters
      return /^sk-ant-[a-zA-Z0-9-]{40,}$/.test(cleanKey)

    case 'google':
      // Google AI Studio keys are typically 39 characters alphanumeric
      return /^[\w-]{39}$/.test(cleanKey)

    case 'xai':
      // xAI keys follow similar pattern to OpenAI
      return /^[\w-]{40,}$/.test(cleanKey)

    default:
      // Generic validation - at least 20 characters
      return cleanKey.length >= 20
  }
}

/**
 * Generates a secure random string for encryption keys
 * @param length - Length of the random string
 * @returns Hex encoded random string
 */
export function generateSecureKey(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex')
}
