import { doc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore'
import { createError, defineEvent<PERSON>and<PERSON>, readBody } from 'h3'
import { useFirebaseServer } from '../../firebase/init'
import { getUserSession } from '../../utils/session'
import { decrypt } from '../../../utils/encryption'
import { getLLMProvider } from '../../../config/llm-providers'

interface TestResult {
  success: boolean
  latency: number
  model?: string
  response?: string
  error?: string
  metadata?: {
    tokensUsed?: number
    cost?: number
    provider: string
    timestamp: string
  }
}

/**
 * POST /api/integrations/test
 * Test an integration's connectivity and functionality
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Parse request body
    const body = await readBody(event) as {
      integrationId: string
      testType?: 'connectivity' | 'model_list' | 'simple_chat' | 'full_test'
      model?: string
      message?: string
    }

    if (!body.integrationId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Integration ID is required',
      })
    }

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)

    // Get the integration
    const integrationRef = doc(firestore, 'integrations', body.integrationId)
    const integrationSnap = await getDoc(integrationRef)

    if (!integrationSnap.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Integration not found',
      })
    }

    const integrationData = integrationSnap.data()

    // Check ownership
    if (integrationData.userId !== session.user.uid) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden: Cannot test this integration',
      })
    }

    // Check if integration is active
    if (!integrationData.isActive) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Cannot test inactive integration',
      })
    }

    // Get provider configuration
    const providerConfig = getLLMProvider(integrationData.provider)
    if (!providerConfig) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid provider configuration',
      })
    }

    // Decrypt API key
    const encryptionKey = process.env.NUXT_ENCRYPTION_KEY || process.env.ENCRYPTION_KEY
    if (!encryptionKey) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Encryption not configured',
      })
    }

    let apiKey: string
    try {
      apiKey = decrypt(integrationData.credentials.apiKey, encryptionKey)
    } catch (error) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to decrypt API key',
      })
    }

    // Determine test type
    const testType = body.testType || 'connectivity'
    const startTime = Date.now()

    let testResult: TestResult

    try {
      switch (testType) {
        case 'connectivity':
          testResult = await testConnectivity(integrationData.provider, apiKey)
          break
        case 'model_list':
          testResult = await testModelList(integrationData.provider, apiKey)
          break
        case 'simple_chat':
          testResult = await testSimpleChat(
            integrationData.provider,
            apiKey,
            body.model || integrationData.settings.defaultModel,
            body.message || 'Hello, this is a test message.'
          )
          break
        case 'full_test':
          testResult = await testFullFunctionality(
            integrationData.provider,
            apiKey,
            integrationData.settings
          )
          break
        default:
          throw new Error('Invalid test type')
      }

      testResult.latency = Date.now() - startTime

      // Update integration's last used timestamp on successful test
      if (testResult.success) {
        await updateDoc(integrationRef, {
          lastUsedAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })
      }

    } catch (error) {
      testResult = {
        success: false,
        latency: Date.now() - startTime,
        error: (error as Error).message,
        metadata: {
          provider: integrationData.provider,
          timestamp: new Date().toISOString(),
        },
      }
    }

    return {
      success: true,
      testResult,
      integration: {
        id: body.integrationId,
        name: integrationData.name,
        provider: integrationData.provider,
        model: body.model || integrationData.settings.defaultModel,
      },
      testType,
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to test integration:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to test integration',
    })
  }
})

// Test functions for different providers
async function testConnectivity(provider: string, apiKey: string): Promise<TestResult> {
  switch (provider) {
    case 'openai':
      return await testOpenAIConnectivity(apiKey)
    case 'anthropic':
      return await testAnthropicConnectivity(apiKey)
    case 'google':
      return await testGoogleConnectivity(apiKey)
    case 'xai':
      return await testXAIConnectivity(apiKey)
    default:
      throw new Error(`Connectivity test not supported for provider: ${provider}`)
  }
}

async function testModelList(provider: string, apiKey: string): Promise<TestResult> {
  const result = await testConnectivity(provider, apiKey)
  if (!result.success) return result

  // Try to fetch models
  try {
    switch (provider) {
      case 'openai':
        const openaiResponse = await fetch('https://api.openai.com/v1/models', {
          headers: { 'Authorization': `Bearer ${apiKey}` },
        })
        const openaiData = await openaiResponse.json()
        result.metadata = {
          ...result.metadata,
          modelsCount: openaiData.data?.length || 0,
        }
        break
      case 'google':
        const googleResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`)
        const googleData = await googleResponse.json()
        result.metadata = {
          ...result.metadata,
          modelsCount: googleData.models?.length || 0,
        }
        break
    }
  } catch (error) {
    result.error = `Model list test failed: ${(error as Error).message}`
    result.success = false
  }

  return result
}

async function testSimpleChat(provider: string, apiKey: string, model: string, message: string): Promise<TestResult> {
  try {
    switch (provider) {
      case 'openai':
        const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model,
            messages: [{ role: 'user', content: message }],
            max_tokens: 50,
          }),
        })
        
        if (!openaiResponse.ok) {
          throw new Error(`OpenAI API error: ${openaiResponse.statusText}`)
        }
        
        const openaiData = await openaiResponse.json()
        return {
          success: true,
          latency: 0, // Will be set by caller
          model,
          response: openaiData.choices?.[0]?.message?.content || 'No response',
          metadata: {
            tokensUsed: openaiData.usage?.total_tokens,
            provider,
            timestamp: new Date().toISOString(),
          },
        }

      case 'anthropic':
        const anthropicResponse = await fetch('https://api.anthropic.com/v1/messages', {
          method: 'POST',
          headers: {
            'x-api-key': apiKey,
            'anthropic-version': '2023-06-01',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model,
            messages: [{ role: 'user', content: message }],
            max_tokens: 50,
          }),
        })
        
        if (!anthropicResponse.ok) {
          throw new Error(`Anthropic API error: ${anthropicResponse.statusText}`)
        }
        
        const anthropicData = await anthropicResponse.json()
        return {
          success: true,
          latency: 0,
          model,
          response: anthropicData.content?.[0]?.text || 'No response',
          metadata: {
            tokensUsed: anthropicData.usage?.input_tokens + anthropicData.usage?.output_tokens,
            provider,
            timestamp: new Date().toISOString(),
          },
        }

      default:
        throw new Error(`Chat test not supported for provider: ${provider}`)
    }
  } catch (error) {
    return {
      success: false,
      latency: 0,
      error: (error as Error).message,
      metadata: {
        provider,
        timestamp: new Date().toISOString(),
      },
    }
  }
}

async function testFullFunctionality(provider: string, apiKey: string, settings: any): Promise<TestResult> {
  // Run connectivity test first
  const connectivityResult = await testConnectivity(provider, apiKey)
  if (!connectivityResult.success) return connectivityResult

  // Run model list test
  const modelListResult = await testModelList(provider, apiKey)
  if (!modelListResult.success) return modelListResult

  // Run simple chat test
  const chatResult = await testSimpleChat(
    provider,
    apiKey,
    settings.defaultModel,
    'This is a comprehensive test of the integration.'
  )

  return {
    success: chatResult.success,
    latency: 0, // Will be set by caller
    model: settings.defaultModel,
    response: chatResult.response,
    error: chatResult.error,
    metadata: {
      ...chatResult.metadata,
      testType: 'full_test',
      modelsCount: modelListResult.metadata?.modelsCount,
    },
  }
}

// Provider-specific connectivity tests
async function testOpenAIConnectivity(apiKey: string): Promise<TestResult> {
  const response = await fetch('https://api.openai.com/v1/models', {
    headers: { 'Authorization': `Bearer ${apiKey}` },
  })

  return {
    success: response.ok,
    latency: 0,
    error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`,
    metadata: {
      provider: 'openai',
      timestamp: new Date().toISOString(),
    },
  }
}

async function testAnthropicConnectivity(apiKey: string): Promise<TestResult> {
  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'x-api-key': apiKey,
      'anthropic-version': '2023-06-01',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'claude-3-haiku-20240307',
      messages: [{ role: 'user', content: 'test' }],
      max_tokens: 1,
    }),
  })

  return {
    success: response.status !== 401,
    latency: 0,
    error: response.status === 401 ? 'Invalid API key' : undefined,
    metadata: {
      provider: 'anthropic',
      timestamp: new Date().toISOString(),
    },
  }
}

async function testGoogleConnectivity(apiKey: string): Promise<TestResult> {
  const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`)

  return {
    success: response.ok,
    latency: 0,
    error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`,
    metadata: {
      provider: 'google',
      timestamp: new Date().toISOString(),
    },
  }
}

async function testXAIConnectivity(apiKey: string): Promise<TestResult> {
  const response = await fetch('https://api.x.ai/v1/models', {
    headers: { 'Authorization': `Bearer ${apiKey}` },
  })

  return {
    success: response.ok,
    latency: 0,
    error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`,
    metadata: {
      provider: 'xai',
      timestamp: new Date().toISOString(),
    },
  }
}
