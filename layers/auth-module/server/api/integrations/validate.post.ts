import type { IntegrationValidationResponse } from '../../../types/integration'
import { createError, defineEvent<PERSON>andler, readBody } from 'h3'
import { getLLMProvider } from '../../../config/llm-providers'

// Provider-specific validation functions
async function validateOpenAI(apiKey: string): Promise<IntegrationValidationResponse> {
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const error = await response.text()
      return {
        isValid: false,
        error: response.status === 401 ? 'Invalid API key' : `API error: ${response.statusText}`,
      }
    }

    const data = await response.json()
    const models = data.data?.map((model: any) => ({
      id: model.id,
      name: model.id,
      displayName: model.id,
      contextWindow: 8192, // Default, as OpenAI doesn't provide this in API
      capabilities: ['chat'],
    })) || []

    return {
      isValid: true,
      models,
    }
  }
  catch (error) {
    return {
      isValid: false,
      error: `Failed to validate OpenAI API key: ${(error as Error).message}`,
    }
  }
}

async function validateAnthropic(apiKey: string): Promise<IntegrationValidationResponse> {
  try {
    // Anthropic doesn't have a models endpoint, so we'll validate by making a minimal request
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'claude-3-haiku-20240307',
        messages: [{ role: 'user', content: 'Hi' }],
        max_tokens: 1,
      }),
    })

    if (response.status === 401) {
      return {
        isValid: false,
        error: 'Invalid API key',
      }
    }

    // Even if we get a different error (like rate limit), the key is valid
    if (response.status === 401 || response.status === 403) {
      return {
        isValid: false,
        error: 'Invalid or unauthorized API key',
      }
    }

    // For Anthropic, we'll return the static model list from our config
    const provider = getLLMProvider('anthropic')
    return {
      isValid: true,
      models: provider?.models || [],
    }
  }
  catch (error) {
    return {
      isValid: false,
      error: `Failed to validate Anthropic API key: ${(error as Error).message}`,
    }
  }
}

async function validateGoogle(apiKey: string): Promise<IntegrationValidationResponse> {
  try {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`)

    if (!response.ok) {
      return {
        isValid: false,
        error: response.status === 400 || response.status === 401 ? 'Invalid API key' : `API error: ${response.statusText}`,
      }
    }

    const data = await response.json()
    const models = data.models?.map((model: any) => ({
      id: model.name?.replace('models/', ''),
      name: model.name?.replace('models/', ''),
      displayName: model.displayName || model.name,
      contextWindow: model.inputTokenLimit || 32768,
      capabilities: model.supportedGenerationMethods || ['generateContent'],
    })) || []

    return {
      isValid: true,
      models,
    }
  }
  catch (error) {
    return {
      isValid: false,
      error: `Failed to validate Google AI API key: ${(error as Error).message}`,
    }
  }
}

async function validateXAI(apiKey: string): Promise<IntegrationValidationResponse> {
  try {
    const response = await fetch('https://api.x.ai/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      return {
        isValid: false,
        error: response.status === 401 ? 'Invalid API key' : `API error: ${response.statusText}`,
      }
    }

    const data = await response.json()
    const models = data.data?.map((model: any) => ({
      id: model.id,
      name: model.id,
      displayName: model.id,
      contextWindow: 131072, // Default for Grok models
      capabilities: ['chat'],
    })) || []

    return {
      isValid: true,
      models,
    }
  }
  catch (error) {
    return {
      isValid: false,
      error: `Failed to validate xAI API key: ${(error as Error).message}`,
    }
  }
}

export default defineEventHandler(async (event) => {
  try {
    // Read request body
    const body = await readBody(event)
    const { apiKey, provider } = body

    // Validate input
    if (!apiKey || typeof apiKey !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'API key is required',
      })
    }

    if (!provider || typeof provider !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Provider is required',
      })
    }

    // Validate based on provider
    let result: IntegrationValidationResponse

    switch (provider) {
      case 'openai':
        result = await validateOpenAI(apiKey)
        break
      case 'anthropic':
        result = await validateAnthropic(apiKey)
        break
      case 'google':
        result = await validateGoogle(apiKey)
        break
      case 'xai':
        result = await validateXAI(apiKey)
        break
      default:
        throw createError({
          statusCode: 400,
          statusMessage: 'Unsupported provider',
        })
    }

    return result
  }
  catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Validation error:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to validate API key',
    })
  }
})
