import { collection, query, where, getDocs, doc, updateDoc, deleteDoc, serverTimestamp } from 'firebase/firestore'
import { createError, defineEventHandler, readBody } from 'h3'
import { useFirebaseServer } from '../../../firebase/init'
import { getUserSession } from '../../../utils/session'

interface ConflictResolution {
  integrationId: string
  resolution: 'keep_profile' | 'keep_user' | 'keep_newest' | 'use_profile' | 'use_inherited' | 'merge_settings' | 'create_independent' | 'deactivate' | 'find_alternative'
}

interface ResolutionResult {
  integrationId: string
  action: string
  success: boolean
  error?: string
}

/**
 * POST /api/integrations/conflicts/resolve
 * Resolve integration conflicts with specified strategies
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Parse request body
    const body = await readBody(event) as {
      resolutions: ConflictResolution[]
      autoResolve?: boolean
    }

    if (!body.resolutions || !Array.isArray(body.resolutions)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Resolutions array is required',
      })
    }

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)

    const results: ResolutionResult[] = []
    const integrationsRef = collection(firestore, 'integrations')

    // Process each resolution
    for (const resolution of body.resolutions) {
      try {
        const integrationRef = doc(firestore, 'integrations', resolution.integrationId)
        const integrationSnap = await getDoc(integrationRef)

        if (!integrationSnap.exists()) {
          results.push({
            integrationId: resolution.integrationId,
            action: 'skip',
            success: false,
            error: 'Integration not found',
          })
          continue
        }

        const integrationData = integrationSnap.data()

        // Verify ownership
        if (integrationData.userId !== session.user.uid) {
          results.push({
            integrationId: resolution.integrationId,
            action: 'skip',
            success: false,
            error: 'Unauthorized',
          })
          continue
        }

        // Apply resolution strategy
        switch (resolution.resolution) {
          case 'keep_profile':
            // Deactivate user-level integration, keep profile integration
            if (integrationData.profileId) {
              // This is the profile integration - keep it active
              await updateDoc(integrationRef, {
                isActive: true,
                updatedAt: serverTimestamp(),
              })
              
              // Find and deactivate user-level integration for same provider
              const userIntegrationQuery = query(
                integrationsRef,
                where('userId', '==', session.user.uid),
                where('provider', '==', integrationData.provider),
                where('profileId', '==', null),
                where('isActive', '==', true)
              )
              
              const userIntegrationSnapshot = await getDocs(userIntegrationQuery)
              for (const userDoc of userIntegrationSnapshot.docs) {
                await updateDoc(userDoc.ref, {
                  isActive: false,
                  updatedAt: serverTimestamp(),
                })
              }
              
              results.push({
                integrationId: resolution.integrationId,
                action: 'kept_profile_deactivated_user',
                success: true,
              })
            }
            break

          case 'keep_user':
            // Keep user-level integration, remove profile override
            if (integrationData.profileId) {
              await deleteDoc(integrationRef)
              results.push({
                integrationId: resolution.integrationId,
                action: 'removed_profile_override',
                success: true,
              })
            }
            break

          case 'keep_newest':
            // Keep the most recently created integration
            const sameProviderQuery = query(
              integrationsRef,
              where('userId', '==', session.user.uid),
              where('provider', '==', integrationData.provider),
              where('isActive', '==', true)
            )
            
            const sameProviderSnapshot = await getDocs(sameProviderQuery)
            const integrations = sameProviderSnapshot.docs.map(doc => ({
              id: doc.id,
              ref: doc.ref,
              data: doc.data(),
            }))
            
            // Sort by creation date (newest first)
            integrations.sort((a, b) => {
              const aTime = a.data.createdAt?.toMillis() || 0
              const bTime = b.data.createdAt?.toMillis() || 0
              return bTime - aTime
            })
            
            // Keep the newest, deactivate others
            for (let i = 1; i < integrations.length; i++) {
              await updateDoc(integrations[i].ref, {
                isActive: false,
                updatedAt: serverTimestamp(),
              })
            }
            
            results.push({
              integrationId: resolution.integrationId,
              action: 'kept_newest',
              success: true,
            })
            break

          case 'create_independent':
            // Convert profile override to independent integration
            if (integrationData.profileId && integrationData.parentIntegrationId) {
              await updateDoc(integrationRef, {
                parentIntegrationId: null,
                name: `${integrationData.name} (Independent)`,
                updatedAt: serverTimestamp(),
              })
              
              results.push({
                integrationId: resolution.integrationId,
                action: 'converted_to_independent',
                success: true,
              })
            }
            break

          case 'deactivate':
            // Simply deactivate the integration
            await updateDoc(integrationRef, {
              isActive: false,
              updatedAt: serverTimestamp(),
            })
            
            results.push({
              integrationId: resolution.integrationId,
              action: 'deactivated',
              success: true,
            })
            break

          case 'merge_settings':
            // Merge settings from parent integration
            if (integrationData.profileId && integrationData.parentIntegrationId) {
              const parentRef = doc(firestore, 'integrations', integrationData.parentIntegrationId)
              const parentSnap = await getDoc(parentRef)
              
              if (parentSnap.exists()) {
                const parentData = parentSnap.data()
                const mergedSettings = {
                  ...parentData.settings,
                  ...integrationData.settings,
                }
                
                await updateDoc(integrationRef, {
                  settings: mergedSettings,
                  updatedAt: serverTimestamp(),
                })
                
                results.push({
                  integrationId: resolution.integrationId,
                  action: 'merged_settings',
                  success: true,
                })
              }
            }
            break

          default:
            results.push({
              integrationId: resolution.integrationId,
              action: 'skip',
              success: false,
              error: 'Unknown resolution strategy',
            })
        }
      } catch (error) {
        results.push({
          integrationId: resolution.integrationId,
          action: 'error',
          success: false,
          error: (error as Error).message,
        })
      }
    }

    // Calculate summary
    const summary = {
      totalResolutions: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      actions: results.reduce((acc, result) => {
        acc[result.action] = (acc[result.action] || 0) + 1
        return acc
      }, {} as Record<string, number>),
    }

    return {
      success: true,
      results,
      summary,
      message: `Resolved ${summary.successful}/${summary.totalResolutions} conflicts`,
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to resolve conflicts:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to resolve conflicts',
    })
  }
})
