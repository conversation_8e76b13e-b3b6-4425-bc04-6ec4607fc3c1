import { createError, define<PERSON><PERSON><PERSON><PERSON><PERSON>, getQuery } from 'h3'
import { getAllLLMProviders, getLL<PERSON>rovider } from '../../../config/llm-providers'

// Cache for model data (24 hour TTL)
const modelCache = new Map<string, { data: any, timestamp: number }>()
const CACHE_TTL = 24 * 60 * 60 * 1000 // 24 hours

export default defineEventHandler(async (event) => {
  try {
    // Get query parameters
    const query = getQuery(event)
    const { provider } = query

    // If no provider specified, return all providers with their static models
    if (!provider) {
      const providers = getAllLLMProviders()
      return {
        success: true,
        providers: providers.map(p => ({
          id: p.id,
          name: p.name,
          icon: p.icon,
          description: p.description,
          models: p.models,
        })),
      }
    }

    // Get specific provider
    const providerConfig = getLLMProvider(provider as string)
    if (!providerConfig) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Provider not found',
      })
    }

    // Check cache
    const cacheKey = `models_${provider}`
    const cached = modelCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return {
        success: true,
        provider,
        models: cached.data,
        cached: true,
      }
    }

    // For now, return static models
    // In production, we could fetch dynamic models here with proper API keys
    const models = providerConfig.models

    // Update cache
    modelCache.set(cacheKey, {
      data: models,
      timestamp: Date.now(),
    })

    return {
      success: true,
      provider,
      models,
      cached: false,
    }
  }
  catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Models fetch error:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch models',
    })
  }
})
