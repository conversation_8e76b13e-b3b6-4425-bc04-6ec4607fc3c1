import { collection, query, where, getDocs, type DocumentData } from 'firebase/firestore'
import { createError, defineEventHandler, getQuery } from 'h3'
import { useFirebaseServer } from '../../firebase/init'
import { getUserSession } from '../../utils/session'
import type { Integration } from '../../../types/integration'

interface ConflictDetectionResult {
  integration: Integration
  conflict: {
    type: 'duplicate_provider' | 'conflicting_settings' | 'deprecated_parent'
    message: string
    severity: 'warning' | 'error'
  }
  status: {
    isProfileSpecific: boolean
    isInherited: boolean
    isOverridden: boolean
    isEffective: boolean
    source: 'profile' | 'user'
    parentIntegration?: Integration
  }
}

/**
 * GET /api/integrations/conflicts
 * Detect integration conflicts for the current user
 * Query params:
 * - profileId: string - Check conflicts for specific profile
 * - provider: string - Check conflicts for specific provider
 * - severity: 'warning' | 'error' - Filter by conflict severity
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Get query parameters
    const queryParams = getQuery(event)
    const { profileId, provider, severity } = queryParams

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)

    // Get all user's integrations
    const integrationsRef = collection(firestore, 'integrations')
    const userIntegrationsQuery = query(
      integrationsRef,
      where('userId', '==', session.user.uid)
    )

    const userIntegrationsSnapshot = await getDocs(userIntegrationsQuery)
    const allIntegrations: Integration[] = []

    userIntegrationsSnapshot.forEach((doc) => {
      const data = doc.data() as DocumentData
      const integration: Integration = {
        id: doc.id,
        userId: data.userId,
        profileId: data.profileId || undefined,
        provider: data.provider,
        name: data.name,
        description: data.description || '',
        credentials: {
          apiKey: '***',
          encryptedAt: data.credentials?.encryptedAt,
        },
        settings: data.settings || {},
        isActive: data.isActive ?? true,
        isDefault: data.isDefault ?? false,
        availableToProfiles: data.availableToProfiles ?? false,
        lastUsedAt: data.lastUsedAt,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
      }
      allIntegrations.push(integration)
    })

    // Filter integrations if profileId is specified
    let targetIntegrations = allIntegrations
    if (profileId) {
      if (profileId === 'user-level') {
        targetIntegrations = allIntegrations.filter(i => !i.profileId)
      } else {
        // Include both profile-specific and inherited integrations for this profile
        const profileSpecific = allIntegrations.filter(i => i.profileId === profileId)
        const inherited = allIntegrations.filter(i => 
          !i.profileId && 
          i.availableToProfiles && 
          !profileSpecific.some(ps => ps.provider === i.provider && ps.isActive)
        )
        targetIntegrations = [...profileSpecific, ...inherited]
      }
    }

    // Filter by provider if specified
    if (provider && provider !== 'all') {
      targetIntegrations = targetIntegrations.filter(i => i.provider === provider)
    }

    // Detect conflicts
    const conflicts: ConflictDetectionResult[] = []

    // 1. Detect duplicate provider conflicts
    const providerGroups = targetIntegrations.reduce((acc, integration) => {
      if (integration.isActive) {
        const key = profileId && profileId !== 'user-level' 
          ? `${integration.provider}-${profileId}` 
          : `${integration.provider}-${integration.profileId || 'user'}`
        
        if (!acc[key]) {
          acc[key] = []
        }
        acc[key].push(integration)
      }
      return acc
    }, {} as Record<string, Integration[]>)

    Object.values(providerGroups).forEach(integrations => {
      if (integrations.length > 1) {
        integrations.forEach(integration => {
          conflicts.push({
            integration,
            conflict: {
              type: 'duplicate_provider',
              message: `Multiple active integrations for ${integration.provider}`,
              severity: 'warning',
            },
            status: {
              isProfileSpecific: !!integration.profileId,
              isInherited: !integration.profileId && integration.availableToProfiles,
              isOverridden: false, // Will be calculated below
              isEffective: true,
              source: integration.profileId ? 'profile' : 'user',
            },
          })
        })
      }
    })

    // 2. Detect deprecated parent conflicts
    for (const integration of targetIntegrations) {
      if (integration.profileId && integration.parentIntegrationId) {
        const parentIntegration = allIntegrations.find(i => i.id === integration.parentIntegrationId)
        
        if (parentIntegration && !parentIntegration.isActive) {
          conflicts.push({
            integration,
            conflict: {
              type: 'deprecated_parent',
              message: 'Parent integration is no longer active',
              severity: 'error',
            },
            status: {
              isProfileSpecific: true,
              isInherited: false,
              isOverridden: true,
              isEffective: integration.isActive,
              source: 'profile',
              parentIntegration,
            },
          })
        }

        if (parentIntegration && !parentIntegration.availableToProfiles) {
          conflicts.push({
            integration,
            conflict: {
              type: 'deprecated_parent',
              message: 'Parent integration is no longer available to profiles',
              severity: 'error',
            },
            status: {
              isProfileSpecific: true,
              isInherited: false,
              isOverridden: true,
              isEffective: integration.isActive,
              source: 'profile',
              parentIntegration,
            },
          })
        }
      }
    }

    // 3. Detect conflicting settings (basic check)
    for (const integration of targetIntegrations) {
      if (integration.profileId && integration.parentIntegrationId) {
        const parentIntegration = allIntegrations.find(i => i.id === integration.parentIntegrationId)
        
        if (parentIntegration && parentIntegration.isActive) {
          // Check for significant setting differences
          const parentModel = parentIntegration.settings.defaultModel
          const childModel = integration.settings.defaultModel
          
          if (parentModel && childModel && parentModel !== childModel) {
            // This is actually normal behavior, but we can flag it for awareness
            conflicts.push({
              integration,
              conflict: {
                type: 'conflicting_settings',
                message: `Using different model (${childModel}) than parent (${parentModel})`,
                severity: 'warning',
              },
              status: {
                isProfileSpecific: true,
                isInherited: false,
                isOverridden: true,
                isEffective: integration.isActive,
                source: 'profile',
                parentIntegration,
              },
            })
          }
        }
      }
    }

    // Filter by severity if specified
    let filteredConflicts = conflicts
    if (severity) {
      filteredConflicts = conflicts.filter(c => c.conflict.severity === severity)
    }

    // Group conflicts by type for summary
    const conflictSummary = filteredConflicts.reduce((acc, conflict) => {
      const type = conflict.conflict.type
      if (!acc[type]) {
        acc[type] = { count: 0, severity: conflict.conflict.severity }
      }
      acc[type].count++
      return acc
    }, {} as Record<string, { count: number; severity: string }>)

    return {
      success: true,
      conflicts: filteredConflicts,
      summary: {
        totalConflicts: filteredConflicts.length,
        conflictsByType: conflictSummary,
        hasErrors: filteredConflicts.some(c => c.conflict.severity === 'error'),
        hasWarnings: filteredConflicts.some(c => c.conflict.severity === 'warning'),
      },
      filters: {
        profileId,
        provider,
        severity,
      },
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to detect conflicts:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to detect conflicts',
    })
  }
})
