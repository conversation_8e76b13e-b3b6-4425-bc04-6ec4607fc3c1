import { doc, getDoc } from 'firebase/firestore'
import { createError, define<PERSON>vent<PERSON><PERSON><PERSON>, getRouterParam } from 'h3'
import { useFirebaseServer } from '../../firebase/init'
import { getUserSession } from '../../utils/session'
import type { Integration } from '../../../types/integration'

/**
 * GET /api/integrations/[id]
 * Get a specific integration by ID
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Get integration ID from route params
    const integrationId = getRouterParam(event, 'id')
    if (!integrationId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Integration ID is required',
      })
    }

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)

    // Get the integration document
    const integrationRef = doc(firestore, 'integrations', integrationId)
    const integrationSnap = await getDoc(integrationRef)

    if (!integrationSnap.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Integration not found',
      })
    }

    const data = integrationSnap.data()

    // Check ownership - users can only access their own integrations
    if (data.userId !== session.user.uid) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden: Cannot access this integration',
      })
    }

    // Transform to Integration object (without exposing sensitive data)
    const integration: Integration = {
      id: integrationSnap.id,
      userId: data.userId,
      profileId: data.profileId || undefined,
      provider: data.provider,
      name: data.name,
      description: data.description || '',
      credentials: {
        apiKey: '***', // Don't expose the actual API key
        encryptedAt: data.credentials?.encryptedAt,
      },
      settings: data.settings || {},
      isActive: data.isActive ?? true,
      isDefault: data.isDefault ?? false,
      availableToProfiles: data.availableToProfiles ?? false,
      lastUsedAt: data.lastUsedAt,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
    }

    return {
      success: true,
      integration,
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to fetch integration:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch integration',
    })
  }
})
