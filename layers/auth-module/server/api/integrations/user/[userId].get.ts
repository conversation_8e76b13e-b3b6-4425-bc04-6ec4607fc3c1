import { collection, query, where, orderBy, getDocs, type DocumentData } from 'firebase/firestore'
import { createError, defineEventHandler, getRouterParam, getQuery } from 'h3'
import { useFirebaseServer } from '../../../firebase/init'
import { getUserSession } from '../../../utils/session'
import type { Integration } from '../../../../types/integration'

/**
 * GET /api/integrations/user/[userId]
 * Get all integrations for a specific user
 * Query params:
 * - includeProfileIntegrations: boolean - Include profile-level integrations
 * - provider: string - Filter by provider
 * - isActive: boolean - Filter by active status
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Get user ID from route params
    const userId = getRouterParam(event, 'userId')
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User ID is required',
      })
    }

    // Check authorization - users can only access their own integrations
    if (userId !== session.user.uid) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden: Cannot access other users\' integrations',
      })
    }

    // Get query parameters
    const queryParams = getQuery(event)
    const {
      includeProfileIntegrations = 'false',
      provider,
      isActive,
    } = queryParams

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)

    // Build query constraints
    const constraints: any[] = [
      where('userId', '==', userId),
      orderBy('createdAt', 'desc'),
    ]

    // Profile filtering
    if (includeProfileIntegrations === 'false') {
      // User-level integrations only
      constraints.push(where('profileId', '==', null))
    }
    // If includeProfileIntegrations is true, we include all integrations (no additional filter needed)

    // Provider filtering
    if (provider && provider !== 'all') {
      constraints.push(where('provider', '==', provider))
    }

    // Active status filtering
    if (isActive !== undefined) {
      constraints.push(where('isActive', '==', isActive === 'true'))
    }

    // Execute query
    const integrationsRef = collection(firestore, 'integrations')
    const q = query(integrationsRef, ...constraints)
    const querySnapshot = await getDocs(q)

    // Transform documents to Integration objects
    const integrations: Integration[] = []
    querySnapshot.forEach((doc) => {
      const data = doc.data() as DocumentData
      
      const integration: Integration = {
        id: doc.id,
        userId: data.userId,
        profileId: data.profileId || undefined,
        provider: data.provider,
        name: data.name,
        description: data.description || '',
        credentials: {
          apiKey: '***', // Don't expose the actual API key
          encryptedAt: data.credentials?.encryptedAt,
        },
        settings: data.settings || {},
        isActive: data.isActive ?? true,
        isDefault: data.isDefault ?? false,
        availableToProfiles: data.availableToProfiles ?? false,
        lastUsedAt: data.lastUsedAt,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
      }
      
      integrations.push(integration)
    })

    // Separate user-level and profile-level integrations
    const userLevelIntegrations = integrations.filter(i => !i.profileId)
    const profileLevelIntegrations = integrations.filter(i => i.profileId)

    // Group profile integrations by profile ID
    const profileIntegrationsByProfile: Record<string, Integration[]> = {}
    profileLevelIntegrations.forEach(integration => {
      if (integration.profileId) {
        if (!profileIntegrationsByProfile[integration.profileId]) {
          profileIntegrationsByProfile[integration.profileId] = []
        }
        profileIntegrationsByProfile[integration.profileId].push(integration)
      }
    })

    // Calculate summary statistics
    const summary = {
      totalIntegrations: integrations.length,
      userLevelIntegrations: userLevelIntegrations.length,
      profileLevelIntegrations: profileLevelIntegrations.length,
      availableToProfiles: userLevelIntegrations.filter(i => i.availableToProfiles).length,
      activeIntegrations: integrations.filter(i => i.isActive).length,
      providerCounts: integrations.reduce((acc, integration) => {
        acc[integration.provider] = (acc[integration.provider] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      profileCount: Object.keys(profileIntegrationsByProfile).length,
    }

    return {
      success: true,
      data: {
        userLevelIntegrations,
        profileIntegrationsByProfile: includeProfileIntegrations === 'true' ? profileIntegrationsByProfile : undefined,
        allIntegrations: includeProfileIntegrations === 'true' ? integrations : userLevelIntegrations,
      },
      summary,
      filters: {
        userId,
        includeProfileIntegrations: includeProfileIntegrations === 'true',
        provider,
        isActive,
      },
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to fetch user integrations:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch user integrations',
    })
  }
})
