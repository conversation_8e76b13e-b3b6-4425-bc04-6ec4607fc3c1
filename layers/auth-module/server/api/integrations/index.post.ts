import { collection, addDoc, serverTimestamp, query, where, getDocs } from 'firebase/firestore'
import { createError, defineEventHandler, readBody } from 'h3'
import { useFirebaseServer } from '../../firebase/init'
import { getUserSession } from '../../utils/session'
import { getLLMProvider } from '../../../config/llm-providers'
import { encrypt } from '../../../utils/encryption'
import type { Integration, IntegrationFormData } from '../../../types/integration'

/**
 * POST /api/integrations
 * Create a new integration (user-level or profile-level)
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Parse request body
    const body = await readBody(event) as IntegrationFormData & {
      profileId?: string
      parentIntegrationId?: string
    }

    // Validate required fields
    if (!body.name || !body.provider || !body.apiKey) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields: name, provider, apiKey',
      })
    }

    // Validate provider
    const providerConfig = getLLMProvider(body.provider)
    if (!providerConfig) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid provider',
      })
    }

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)

    // Check for duplicate integrations
    const integrationsRef = collection(firestore, 'integrations')
    const duplicateQuery = query(
      integrationsRef,
      where('userId', '==', session.user.uid),
      where('provider', '==', body.provider),
      where('profileId', '==', body.profileId || null),
      where('isActive', '==', true)
    )
    
    const duplicateSnapshot = await getDocs(duplicateQuery)
    if (!duplicateSnapshot.empty) {
      throw createError({
        statusCode: 409,
        statusMessage: `Active ${body.provider} integration already exists for this ${body.profileId ? 'profile' : 'user'}`,
      })
    }

    // Get encryption key
    const encryptionKey = process.env.NUXT_ENCRYPTION_KEY || process.env.ENCRYPTION_KEY
    if (!encryptionKey) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Encryption not configured',
      })
    }

    // Encrypt API key
    const encryptedApiKey = encrypt(body.apiKey, encryptionKey)

    // Prepare integration data
    const now = serverTimestamp()
    const integrationData = {
      userId: session.user.uid,
      profileId: body.profileId || null,
      parentIntegrationId: body.parentIntegrationId || null,
      provider: body.provider,
      name: body.name.trim(),
      description: body.description?.trim() || '',
      credentials: {
        apiKey: encryptedApiKey,
        encryptedAt: now,
      },
      settings: {
        defaultModel: body.settings?.defaultModel || providerConfig.models[0]?.id || '',
        maxTokens: body.settings?.maxTokens || providerConfig.models[0]?.contextWindow || 4096,
        temperature: body.settings?.temperature || 0.7,
        topP: body.settings?.topP || 1,
        frequencyPenalty: body.settings?.frequencyPenalty || 0,
        presencePenalty: body.settings?.presencePenalty || 0,
        ...body.settings,
      },
      isActive: true,
      isDefault: body.isDefault || false,
      availableToProfiles: body.profileId ? false : (body.availableToProfiles || false), // Profile integrations are not available to other profiles
      lastUsedAt: null,
      createdAt: now,
      updatedAt: now,
    }

    // If this is set as default, unset other defaults for the same user/profile and provider
    if (body.isDefault) {
      const defaultQuery = query(
        integrationsRef,
        where('userId', '==', session.user.uid),
        where('provider', '==', body.provider),
        where('profileId', '==', body.profileId || null),
        where('isDefault', '==', true)
      )
      
      const defaultSnapshot = await getDocs(defaultQuery)
      // Note: In a production app, you'd want to update these in a transaction
      // For now, we'll just log this potential race condition
      if (!defaultSnapshot.empty) {
        console.warn(`Setting new default ${body.provider} integration while others exist`)
      }
    }

    // Create the integration
    const docRef = await addDoc(integrationsRef, integrationData)

    // Return the created integration (without sensitive data)
    const createdIntegration: Integration = {
      id: docRef.id,
      userId: session.user.uid,
      profileId: body.profileId,
      provider: body.provider,
      name: body.name.trim(),
      description: body.description?.trim() || '',
      credentials: {
        apiKey: '***', // Don't return the actual API key
        encryptedAt: integrationData.credentials.encryptedAt,
      },
      settings: integrationData.settings,
      isActive: true,
      isDefault: body.isDefault || false,
      availableToProfiles: integrationData.availableToProfiles,
      lastUsedAt: null,
      createdAt: integrationData.createdAt,
      updatedAt: integrationData.updatedAt,
    }

    return {
      success: true,
      integration: createdIntegration,
      message: `${body.provider} integration created successfully`,
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to create integration:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create integration',
    })
  }
})
