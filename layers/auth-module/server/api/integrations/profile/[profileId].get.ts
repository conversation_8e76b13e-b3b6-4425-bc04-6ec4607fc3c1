import { collection, query, where, orderBy, getDocs, type DocumentData } from 'firebase/firestore'
import { createError, defineEventHandler, getRouterParam, getQuery } from 'h3'
import { useFirebaseServer } from '../../../firebase/init'
import { getUserSession } from '../../../utils/session'
import type { Integration } from '../../../../types/integration'

/**
 * GET /api/integrations/profile/[profileId]
 * Get all integrations for a specific profile, including inherited and profile-specific
 * Query params:
 * - includeInherited: boolean - Include inherited user-level integrations
 * - provider: string - Filter by provider
 * - isActive: boolean - Filter by active status
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Get profile ID from route params
    const profileId = getRouterParam(event, 'profileId')
    if (!profileId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Profile ID is required',
      })
    }

    // Get query parameters
    const queryParams = getQuery(event)
    const {
      includeInherited = 'true',
      provider,
      isActive,
    } = queryParams

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)

    const integrationsRef = collection(firestore, 'integrations')
    const allIntegrations: Integration[] = []

    // Get profile-specific integrations
    const profileConstraints: any[] = [
      where('userId', '==', session.user.uid),
      where('profileId', '==', profileId),
      orderBy('createdAt', 'desc'),
    ]

    if (provider && provider !== 'all') {
      profileConstraints.push(where('provider', '==', provider))
    }

    if (isActive !== undefined) {
      profileConstraints.push(where('isActive', '==', isActive === 'true'))
    }

    const profileQuery = query(integrationsRef, ...profileConstraints)
    const profileSnapshot = await getDocs(profileQuery)

    const profileIntegrations: Integration[] = []
    profileSnapshot.forEach((doc) => {
      const data = doc.data() as DocumentData
      const integration: Integration = {
        id: doc.id,
        userId: data.userId,
        profileId: data.profileId,
        provider: data.provider,
        name: data.name,
        description: data.description || '',
        credentials: {
          apiKey: '***',
          encryptedAt: data.credentials?.encryptedAt,
        },
        settings: data.settings || {},
        isActive: data.isActive ?? true,
        isDefault: data.isDefault ?? false,
        availableToProfiles: data.availableToProfiles ?? false,
        lastUsedAt: data.lastUsedAt,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
      }
      profileIntegrations.push(integration)
      allIntegrations.push(integration)
    })

    // Get inherited user-level integrations if requested
    let inheritedIntegrations: Integration[] = []
    if (includeInherited === 'true') {
      const userConstraints: any[] = [
        where('userId', '==', session.user.uid),
        where('profileId', '==', null), // User-level only
        where('availableToProfiles', '==', true), // Available for inheritance
        orderBy('createdAt', 'desc'),
      ]

      if (provider && provider !== 'all') {
        userConstraints.push(where('provider', '==', provider))
      }

      if (isActive !== undefined) {
        userConstraints.push(where('isActive', '==', isActive === 'true'))
      }

      const userQuery = query(integrationsRef, ...userConstraints)
      const userSnapshot = await getDocs(userQuery)

      userSnapshot.forEach((doc) => {
        const data = doc.data() as DocumentData
        
        // Check if this provider is overridden by a profile integration
        const isOverridden = profileIntegrations.some(pi => pi.provider === data.provider && pi.isActive)
        
        if (!isOverridden) {
          const integration: Integration = {
            id: doc.id,
            userId: data.userId,
            profileId: undefined,
            provider: data.provider,
            name: data.name,
            description: data.description || '',
            credentials: {
              apiKey: '***',
              encryptedAt: data.credentials?.encryptedAt,
            },
            settings: data.settings || {},
            isActive: data.isActive ?? true,
            isDefault: data.isDefault ?? false,
            availableToProfiles: data.availableToProfiles ?? false,
            lastUsedAt: data.lastUsedAt,
            createdAt: data.createdAt,
            updatedAt: data.updatedAt,
          }
          inheritedIntegrations.push(integration)
          allIntegrations.push(integration)
        }
      })
    }

    // Determine effective integrations (profile-specific takes precedence over inherited)
    const effectiveIntegrations: Integration[] = []
    const usedProviders = new Set<string>()

    // Add profile-specific integrations first (they have priority)
    profileIntegrations.forEach(integration => {
      if (integration.isActive) {
        effectiveIntegrations.push(integration)
        usedProviders.add(integration.provider)
      }
    })

    // Add inherited integrations that aren't overridden
    inheritedIntegrations.forEach(integration => {
      if (integration.isActive && !usedProviders.has(integration.provider)) {
        effectiveIntegrations.push(integration)
        usedProviders.add(integration.provider)
      }
    })

    // Detect conflicts (multiple active integrations for same provider)
    const conflicts: Array<{
      provider: string
      integrations: Integration[]
      type: 'duplicate_provider'
    }> = []

    const providerGroups = allIntegrations.reduce((acc, integration) => {
      if (integration.isActive) {
        if (!acc[integration.provider]) {
          acc[integration.provider] = []
        }
        acc[integration.provider].push(integration)
      }
      return acc
    }, {} as Record<string, Integration[]>)

    Object.entries(providerGroups).forEach(([provider, integrations]) => {
      if (integrations.length > 1) {
        conflicts.push({
          provider,
          integrations,
          type: 'duplicate_provider',
        })
      }
    })

    // Calculate summary statistics
    const summary = {
      totalIntegrations: allIntegrations.length,
      profileSpecificIntegrations: profileIntegrations.length,
      inheritedIntegrations: inheritedIntegrations.length,
      effectiveIntegrations: effectiveIntegrations.length,
      activeIntegrations: allIntegrations.filter(i => i.isActive).length,
      conflicts: conflicts.length,
      providerCounts: allIntegrations.reduce((acc, integration) => {
        acc[integration.provider] = (acc[integration.provider] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      inheritanceEfficiency: inheritedIntegrations.length > 0 
        ? Math.round((inheritedIntegrations.length / (inheritedIntegrations.length + profileIntegrations.length)) * 100)
        : 0,
    }

    return {
      success: true,
      data: {
        profileSpecificIntegrations: profileIntegrations,
        inheritedIntegrations,
        effectiveIntegrations,
        allIntegrations,
      },
      conflicts,
      summary,
      filters: {
        profileId,
        includeInherited: includeInherited === 'true',
        provider,
        isActive,
      },
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to fetch profile integrations:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch profile integrations',
    })
  }
})
