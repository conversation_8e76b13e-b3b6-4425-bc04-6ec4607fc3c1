import { collection, addDoc, serverTimestamp, query, where, getDocs, doc, getDoc } from 'firebase/firestore'
import { createError, defineEventHandler, getRouterParam, readBody } from 'h3'
import { useFirebaseServer } from '../../../../firebase/init'
import { getUserSession } from '../../../../utils/session'
import { encrypt } from '../../../../../utils/encryption'
import type { Integration, IntegrationFormData } from '../../../../../types/integration'

/**
 * POST /api/integrations/profile/[profileId]/override
 * Create a profile override for an inherited user-level integration
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Get profile ID from route params
    const profileId = getRouterParam(event, 'profileId')
    if (!profileId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Profile ID is required',
      })
    }

    // Parse request body
    const body = await readBody(event) as IntegrationFormData & {
      parentIntegrationId: string
    }

    // Validate required fields
    if (!body.parentIntegrationId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Parent integration ID is required for overrides',
      })
    }

    if (!body.name || !body.provider || !body.apiKey) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields: name, provider, apiKey',
      })
    }

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)

    // Verify the parent integration exists and is owned by the user
    const parentIntegrationRef = doc(firestore, 'integrations', body.parentIntegrationId)
    const parentIntegrationSnap = await getDoc(parentIntegrationRef)

    if (!parentIntegrationSnap.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Parent integration not found',
      })
    }

    const parentData = parentIntegrationSnap.data()

    // Check ownership of parent integration
    if (parentData.userId !== session.user.uid) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden: Cannot override this integration',
      })
    }

    // Verify parent integration is user-level and available to profiles
    if (parentData.profileId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Cannot override a profile-level integration',
      })
    }

    if (!parentData.availableToProfiles) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Parent integration is not available to profiles',
      })
    }

    // Verify provider matches
    if (body.provider !== parentData.provider) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Provider must match parent integration',
      })
    }

    // Check if an override already exists for this profile and provider
    const integrationsRef = collection(firestore, 'integrations')
    const existingOverrideQuery = query(
      integrationsRef,
      where('userId', '==', session.user.uid),
      where('profileId', '==', profileId),
      where('provider', '==', body.provider),
      where('isActive', '==', true)
    )

    const existingOverrideSnapshot = await getDocs(existingOverrideQuery)
    if (!existingOverrideSnapshot.empty) {
      throw createError({
        statusCode: 409,
        statusMessage: `Profile override for ${body.provider} already exists`,
      })
    }

    // Get encryption key
    const encryptionKey = process.env.NUXT_ENCRYPTION_KEY || process.env.ENCRYPTION_KEY
    if (!encryptionKey) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Encryption not configured',
      })
    }

    // Encrypt API key
    const encryptedApiKey = encrypt(body.apiKey, encryptionKey)

    // Prepare override integration data
    const now = serverTimestamp()
    const overrideData = {
      userId: session.user.uid,
      profileId,
      parentIntegrationId: body.parentIntegrationId,
      provider: body.provider,
      name: body.name.trim(),
      description: body.description?.trim() || '',
      credentials: {
        apiKey: encryptedApiKey,
        encryptedAt: now,
      },
      settings: {
        // Start with parent settings as defaults
        ...parentData.settings,
        // Override with provided settings
        ...body.settings,
      },
      isActive: true,
      isDefault: body.isDefault || false,
      availableToProfiles: false, // Profile integrations are not available to other profiles
      lastUsedAt: null,
      createdAt: now,
      updatedAt: now,
    }

    // Create the override integration
    const docRef = await addDoc(integrationsRef, overrideData)

    // Return the created override (without sensitive data)
    const createdOverride: Integration = {
      id: docRef.id,
      userId: session.user.uid,
      profileId,
      provider: body.provider,
      name: body.name.trim(),
      description: body.description?.trim() || '',
      credentials: {
        apiKey: '***', // Don't return the actual API key
        encryptedAt: overrideData.credentials.encryptedAt,
      },
      settings: overrideData.settings,
      isActive: true,
      isDefault: body.isDefault || false,
      availableToProfiles: false,
      lastUsedAt: null,
      createdAt: overrideData.createdAt,
      updatedAt: overrideData.updatedAt,
    }

    return {
      success: true,
      integration: createdOverride,
      parentIntegration: {
        id: body.parentIntegrationId,
        name: parentData.name,
        provider: parentData.provider,
      },
      message: `Profile override for ${body.provider} created successfully`,
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to create profile override:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create profile override',
    })
  }
})
