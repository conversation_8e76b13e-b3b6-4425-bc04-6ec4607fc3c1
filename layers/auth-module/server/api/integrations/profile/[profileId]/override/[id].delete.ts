import { doc, getDoc, deleteDoc } from 'firebase/firestore'
import { createError, defineEventHandler, getRouterParam } from 'h3'
import { useFirebaseServer } from '../../../../../firebase/init'
import { getUserSession } from '../../../../../utils/session'

/**
 * DELETE /api/integrations/profile/[profileId]/override/[id]
 * Remove a profile override and revert to inherited integration
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Get route parameters
    const profileId = getRouterParam(event, 'profileId')
    const integrationId = getRouterParam(event, 'id')

    if (!profileId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Profile ID is required',
      })
    }

    if (!integrationId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Integration ID is required',
      })
    }

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)

    // Get the integration to delete
    const integrationRef = doc(firestore, 'integrations', integrationId)
    const integrationSnap = await getDoc(integrationRef)

    if (!integrationSnap.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Integration not found',
      })
    }

    const integrationData = integrationSnap.data()

    // Verify ownership
    if (integrationData.userId !== session.user.uid) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden: Cannot delete this integration',
      })
    }

    // Verify this is a profile integration for the specified profile
    if (integrationData.profileId !== profileId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Integration does not belong to the specified profile',
      })
    }

    // Verify this is actually an override (has a parent integration)
    if (!integrationData.parentIntegrationId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'This is not a profile override - use regular delete endpoint',
      })
    }

    // Get parent integration info for response
    let parentIntegrationInfo = null
    if (integrationData.parentIntegrationId) {
      const parentRef = doc(firestore, 'integrations', integrationData.parentIntegrationId)
      const parentSnap = await getDoc(parentRef)
      
      if (parentSnap.exists()) {
        const parentData = parentSnap.data()
        parentIntegrationInfo = {
          id: parentSnap.id,
          name: parentData.name,
          provider: parentData.provider,
          isActive: parentData.isActive,
          availableToProfiles: parentData.availableToProfiles,
        }
      }
    }

    // Delete the override integration
    await deleteDoc(integrationRef)

    return {
      success: true,
      message: 'Profile override removed successfully',
      deletedOverride: {
        id: integrationId,
        name: integrationData.name,
        provider: integrationData.provider,
        profileId,
      },
      revertedToParent: parentIntegrationInfo,
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to remove profile override:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to remove profile override',
    })
  }
})
