import { doc, getDoc, updateDoc, serverTimestamp, query, where, getDocs, collection } from 'firebase/firestore'
import { createError, defineEvent<PERSON>and<PERSON>, getRouterParam, readBody } from 'h3'
import { useFirebaseServer } from '../../firebase/init'
import { getUserSession } from '../../utils/session'
import { encrypt } from '../../../utils/encryption'
import type { Integration } from '../../../types/integration'

/**
 * PATCH /api/integrations/[id]
 * Update an existing integration
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Get integration ID from route params
    const integrationId = getRouterParam(event, 'id')
    if (!integrationId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Integration ID is required',
      })
    }

    // Parse request body
    const updates = await readBody(event)
    if (!updates || Object.keys(updates).length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No updates provided',
      })
    }

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)

    // Get the existing integration
    const integrationRef = doc(firestore, 'integrations', integrationId)
    const integrationSnap = await getDoc(integrationRef)

    if (!integrationSnap.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Integration not found',
      })
    }

    const existingData = integrationSnap.data()

    // Check ownership
    if (existingData.userId !== session.user.uid) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden: Cannot update this integration',
      })
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: serverTimestamp(),
    }

    // Handle specific field updates
    if (updates.name !== undefined) {
      if (!updates.name.trim()) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Name cannot be empty',
        })
      }
      updateData.name = updates.name.trim()
    }

    if (updates.description !== undefined) {
      updateData.description = updates.description.trim()
    }

    if (updates.isActive !== undefined) {
      updateData.isActive = Boolean(updates.isActive)
    }

    if (updates.isDefault !== undefined) {
      updateData.isDefault = Boolean(updates.isDefault)
      
      // If setting as default, unset other defaults for the same user/profile and provider
      if (updates.isDefault) {
        const integrationsRef = collection(firestore, 'integrations')
        const defaultQuery = query(
          integrationsRef,
          where('userId', '==', session.user.uid),
          where('provider', '==', existingData.provider),
          where('profileId', '==', existingData.profileId || null),
          where('isDefault', '==', true)
        )
        
        const defaultSnapshot = await getDocs(defaultQuery)
        // Note: In production, you'd want to handle this in a transaction
        if (!defaultSnapshot.empty) {
          console.warn(`Setting new default ${existingData.provider} integration while others exist`)
        }
      }
    }

    if (updates.availableToProfiles !== undefined) {
      // Only user-level integrations can be made available to profiles
      if (existingData.profileId) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Profile-level integrations cannot be made available to other profiles',
        })
      }
      updateData.availableToProfiles = Boolean(updates.availableToProfiles)
    }

    if (updates.settings !== undefined) {
      // Merge settings with existing settings
      updateData.settings = {
        ...existingData.settings,
        ...updates.settings,
      }
    }

    // Handle API key update (requires encryption)
    if (updates.apiKey !== undefined) {
      if (!updates.apiKey.trim()) {
        throw createError({
          statusCode: 400,
          statusMessage: 'API key cannot be empty',
        })
      }

      // Get encryption key
      const encryptionKey = process.env.NUXT_ENCRYPTION_KEY || process.env.ENCRYPTION_KEY
      if (!encryptionKey) {
        throw createError({
          statusCode: 500,
          statusMessage: 'Encryption not configured',
        })
      }

      // Encrypt the new API key
      const encryptedApiKey = encrypt(updates.apiKey, encryptionKey)
      updateData.credentials = {
        apiKey: encryptedApiKey,
        encryptedAt: serverTimestamp(),
      }
    }

    // Update last used timestamp if this is a usage update
    if (updates.markAsUsed) {
      updateData.lastUsedAt = serverTimestamp()
    }

    // Perform the update
    await updateDoc(integrationRef, updateData)

    // Fetch the updated integration
    const updatedSnap = await getDoc(integrationRef)
    const updatedData = updatedSnap.data()

    // Return the updated integration (without sensitive data)
    const updatedIntegration: Integration = {
      id: updatedSnap.id,
      userId: updatedData!.userId,
      profileId: updatedData!.profileId || undefined,
      provider: updatedData!.provider,
      name: updatedData!.name,
      description: updatedData!.description || '',
      credentials: {
        apiKey: '***', // Don't expose the actual API key
        encryptedAt: updatedData!.credentials?.encryptedAt,
      },
      settings: updatedData!.settings || {},
      isActive: updatedData!.isActive ?? true,
      isDefault: updatedData!.isDefault ?? false,
      availableToProfiles: updatedData!.availableToProfiles ?? false,
      lastUsedAt: updatedData!.lastUsedAt,
      createdAt: updatedData!.createdAt,
      updatedAt: updatedData!.updatedAt,
    }

    return {
      success: true,
      integration: updatedIntegration,
      message: 'Integration updated successfully',
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to update integration:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update integration',
    })
  }
})
