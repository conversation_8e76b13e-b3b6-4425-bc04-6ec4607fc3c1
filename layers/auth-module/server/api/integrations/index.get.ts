import { collection, query, where, orderBy, limit, getDocs, type DocumentData } from 'firebase/firestore'
import { createError, defineEventHandler, getQuery } from 'h3'
import { useFirebaseServer } from '../../firebase/init'
import { getUserSession } from '../../utils/session'
import type { Integration } from '../../../types/integration'

/**
 * GET /api/integrations
 * List integrations with filtering and pagination
 * Supports user-level and profile-level filtering
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Get query parameters
    const queryParams = getQuery(event)
    const {
      userId,
      profileId,
      provider,
      isActive,
      availableToProfiles,
      page = '1',
      limit: limitParam = '50',
      orderBy: orderByParam = 'createdAt',
      orderDirection = 'desc',
    } = queryParams

    // Validate pagination parameters
    const pageNum = Math.max(1, parseInt(page as string, 10))
    const limitNum = Math.min(100, Math.max(1, parseInt(limitParam as string, 10)))
    const offset = (pageNum - 1) * limitNum

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)
    
    // Build query constraints
    const constraints: any[] = []
    
    // User access control - users can only see their own integrations
    if (userId) {
      // Requesting specific user's integrations
      if (userId !== session.user.uid) {
        throw createError({
          statusCode: 403,
          statusMessage: 'Forbidden: Cannot access other users\' integrations',
        })
      }
      constraints.push(where('userId', '==', userId))
    } else {
      // Default to current user's integrations
      constraints.push(where('userId', '==', session.user.uid))
    }

    // Profile filtering
    if (profileId) {
      if (profileId === 'null' || profileId === 'user-level') {
        // User-level integrations only
        constraints.push(where('profileId', '==', null))
      } else {
        // Specific profile integrations
        constraints.push(where('profileId', '==', profileId))
      }
    }

    // Provider filtering
    if (provider && provider !== 'all') {
      constraints.push(where('provider', '==', provider))
    }

    // Active status filtering
    if (isActive !== undefined) {
      constraints.push(where('isActive', '==', isActive === 'true'))
    }

    // Profile availability filtering
    if (availableToProfiles !== undefined) {
      constraints.push(where('availableToProfiles', '==', availableToProfiles === 'true'))
    }

    // Add ordering
    const validOrderFields = ['createdAt', 'updatedAt', 'lastUsedAt', 'name', 'provider']
    const orderField = validOrderFields.includes(orderByParam as string) ? orderByParam as string : 'createdAt'
    const direction = orderDirection === 'asc' ? 'asc' : 'desc'
    constraints.push(orderBy(orderField, direction))

    // Add limit
    constraints.push(limit(limitNum))

    // Execute query
    const integrationsRef = collection(firestore, 'integrations')
    const q = query(integrationsRef, ...constraints)
    const querySnapshot = await getDocs(q)

    // Transform documents to Integration objects
    const integrations: Integration[] = []
    querySnapshot.forEach((doc) => {
      const data = doc.data() as DocumentData
      
      // Convert Firestore timestamps to our expected format
      const integration: Integration = {
        id: doc.id,
        userId: data.userId,
        profileId: data.profileId || undefined,
        provider: data.provider,
        name: data.name,
        description: data.description,
        credentials: data.credentials,
        settings: data.settings || {},
        isActive: data.isActive ?? true,
        isDefault: data.isDefault ?? false,
        availableToProfiles: data.availableToProfiles ?? false,
        lastUsedAt: data.lastUsedAt,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
      }
      
      integrations.push(integration)
    })

    // Get total count for pagination (simplified - in production, you might want a separate count query)
    const totalCount = integrations.length

    // Apply client-side pagination if needed (for offset)
    const paginatedIntegrations = integrations.slice(offset, offset + limitNum)

    return {
      success: true,
      data: paginatedIntegrations,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limitNum),
        hasNext: pageNum * limitNum < totalCount,
        hasPrev: pageNum > 1,
      },
      filters: {
        userId: userId || session.user.uid,
        profileId,
        provider,
        isActive,
        availableToProfiles,
        orderBy: orderField,
        orderDirection: direction,
      },
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to fetch integrations:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch integrations',
    })
  }
})
