import { doc, getDoc, deleteDoc, collection, query, where, getDocs, updateDoc } from 'firebase/firestore'
import { createError, defineEvent<PERSON><PERSON><PERSON>, getRouterParam, getQuery } from 'h3'
import { useFirebaseServer } from '../../firebase/init'
import { getUserSession } from '../../utils/session'

/**
 * DELETE /api/integrations/[id]
 * Delete an integration
 * Query params:
 * - force: boolean - Force delete even if it has dependent profile integrations
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Get integration ID from route params
    const integrationId = getRouterParam(event, 'id')
    if (!integrationId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Integration ID is required',
      })
    }

    // Get query parameters
    const query_params = getQuery(event)
    const force = query_params.force === 'true'

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)

    // Get the integration to delete
    const integrationRef = doc(firestore, 'integrations', integrationId)
    const integrationSnap = await getDoc(integrationRef)

    if (!integrationSnap.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Integration not found',
      })
    }

    const integrationData = integrationSnap.data()

    // Check ownership
    if (integrationData.userId !== session.user.uid) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden: Cannot delete this integration',
      })
    }

    const integrationsRef = collection(firestore, 'integrations')

    // Check for dependent profile integrations (if this is a user-level integration)
    if (!integrationData.profileId) {
      const dependentQuery = query(
        integrationsRef,
        where('parentIntegrationId', '==', integrationId),
        where('userId', '==', session.user.uid)
      )
      
      const dependentSnapshot = await getDocs(dependentQuery)
      
      if (!dependentSnapshot.empty && !force) {
        const dependentCount = dependentSnapshot.size
        throw createError({
          statusCode: 409,
          statusMessage: `Cannot delete integration: ${dependentCount} profile integration(s) depend on it. Use force=true to delete anyway.`,
        })
      }

      // If force delete, handle dependent integrations
      if (!dependentSnapshot.empty && force) {
        // Option 1: Delete dependent integrations
        // Option 2: Convert dependent integrations to standalone
        // For this implementation, we'll convert them to standalone
        
        const updatePromises = dependentSnapshot.docs.map(async (dependentDoc) => {
          await updateDoc(dependentDoc.ref, {
            parentIntegrationId: null,
            // Could also update the name to indicate it's now standalone
            name: `${dependentDoc.data().name} (Standalone)`,
          })
        })
        
        await Promise.all(updatePromises)
      }
    }

    // Check if this is the default integration and handle accordingly
    if (integrationData.isDefault) {
      // Find other integrations for the same provider and make one of them default
      const sameProviderQuery = query(
        integrationsRef,
        where('userId', '==', session.user.uid),
        where('provider', '==', integrationData.provider),
        where('profileId', '==', integrationData.profileId || null),
        where('isActive', '==', true)
      )
      
      const sameProviderSnapshot = await getDocs(sameProviderQuery)
      const otherIntegrations = sameProviderSnapshot.docs.filter(doc => doc.id !== integrationId)
      
      if (otherIntegrations.length > 0) {
        // Make the first other integration the default
        await updateDoc(otherIntegrations[0].ref, {
          isDefault: true,
        })
      }
    }

    // Delete the integration
    await deleteDoc(integrationRef)

    return {
      success: true,
      message: 'Integration deleted successfully',
      deletedId: integrationId,
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to delete integration:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to delete integration',
    })
  }
})
