import { createError, define<PERSON><PERSON><PERSON><PERSON><PERSON>, readBody } from 'h3'
import { encrypt, validateApiKeyFormat } from '../../../utils/encryption'

// Get encryption key from environment variable
function getEncryptionKey() {
  const key = process.env.NUXT_ENCRYPTION_KEY || process.env.ENCRYPTION_KEY
  if (!key) {
    throw new Error('Encryption key not configured')
  }
  return key
}

export default defineEventHandler(async (event) => {
  try {
    // Read request body
    const body = await readBody(event)
    const { apiKey, provider } = body

    // Validate input
    if (!apiKey || typeof apiKey !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'API key is required',
      })
    }

    if (!provider || typeof provider !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Provider is required',
      })
    }

    // Validate API key format
    if (!validateApiKeyFormat(api<PERSON><PERSON>, provider)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid API key format for provider',
      })
    }

    // Get encryption key
    const encryptionKey = getEncryptionKey()

    // Encrypt the API key
    const encryptedKey = encrypt(apiKey, encryptionKey)

    // Return encrypted key
    return {
      success: true,
      encryptedKey,
      encryptedAt: new Date().toISOString(),
    }
  }
  catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Encryption error:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to encrypt API key',
    })
  }
})
