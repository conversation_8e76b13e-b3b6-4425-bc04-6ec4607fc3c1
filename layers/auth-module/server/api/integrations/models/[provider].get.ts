import { createError, define<PERSON><PERSON><PERSON><PERSON><PERSON>, getR<PERSON>er<PERSON>aram, getQuery } from 'h3'
import { getLLMProvider } from '../../../../config/llm-providers'
import { getUserSession } from '../../../utils/session'

// Enhanced cache with metadata
interface CacheEntry {
  data: any
  timestamp: number
  provider: string
  source: 'static' | 'api'
  version: string
}

const modelCache = new Map<string, CacheEntry>()
const CACHE_TTL = 24 * 60 * 60 * 1000 // 24 hours
const CACHE_VERSION = '1.0'

/**
 * GET /api/integrations/models/[provider]
 * Get models for a specific provider with enhanced caching and validation
 * Query params:
 * - refresh: boolean - Force refresh from API
 * - includeStatic: boolean - Include static model definitions
 * - apiKey: string - Use specific API key for fetching (optional)
 */
export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Get provider from route params
    const provider = getRouterParam(event, 'provider')
    if (!provider) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Provider is required',
      })
    }

    // Get query parameters
    const query = getQuery(event)
    const { refresh = 'false', includeStatic = 'true', apiKey } = query

    // Get provider configuration
    const providerConfig = getLLMProvider(provider)
    if (!providerConfig) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Provider not found',
      })
    }

    const cacheKey = `${provider}-${CACHE_VERSION}`
    const forceRefresh = refresh === 'true'

    // Check cache first (unless force refresh)
    if (!forceRefresh && modelCache.has(cacheKey)) {
      const cached = modelCache.get(cacheKey)!
      const isExpired = Date.now() - cached.timestamp > CACHE_TTL

      if (!isExpired) {
        return {
          success: true,
          provider: {
            id: providerConfig.id,
            name: providerConfig.name,
            icon: providerConfig.icon,
            description: providerConfig.description,
          },
          models: cached.data,
          cache: {
            hit: true,
            timestamp: cached.timestamp,
            source: cached.source,
            age: Date.now() - cached.timestamp,
          },
        }
      }
    }

    // Try to fetch from API if provider supports it and we have an API key
    let apiModels = null
    let apiError = null

    if (providerConfig.apiEndpoint && (apiKey || providerConfig.requiresApiKey === false)) {
      try {
        apiModels = await fetchModelsFromAPI(provider, apiKey as string)
      } catch (error) {
        apiError = (error as Error).message
        console.warn(`Failed to fetch models from ${provider} API:`, error)
      }
    }

    // Determine which models to use
    let models = providerConfig.models // Default to static models
    let source: 'static' | 'api' = 'static'

    if (apiModels && apiModels.length > 0) {
      // Use API models if available
      models = apiModels
      source = 'api'
    } else if (includeStatic === 'false' && !apiModels) {
      // If only API models requested but none available, return error
      throw createError({
        statusCode: 503,
        statusMessage: `API models not available for ${provider}${apiError ? `: ${apiError}` : ''}`,
      })
    }

    // Enhance models with additional metadata
    const enhancedModels = models.map((model: any) => ({
      ...model,
      provider: providerConfig.id,
      providerName: providerConfig.name,
      isApiModel: source === 'api',
      lastUpdated: source === 'api' ? new Date().toISOString() : providerConfig.lastUpdated,
      capabilities: model.capabilities || ['chat'], // Default capability
      pricing: model.pricing || providerConfig.defaultPricing,
    }))

    // Cache the results
    const cacheEntry: CacheEntry = {
      data: enhancedModels,
      timestamp: Date.now(),
      provider,
      source,
      version: CACHE_VERSION,
    }
    modelCache.set(cacheKey, cacheEntry)

    return {
      success: true,
      provider: {
        id: providerConfig.id,
        name: providerConfig.name,
        icon: providerConfig.icon,
        description: providerConfig.description,
        apiEndpoint: providerConfig.apiEndpoint,
        requiresApiKey: providerConfig.requiresApiKey,
      },
      models: enhancedModels,
      cache: {
        hit: false,
        timestamp: cacheEntry.timestamp,
        source: cacheEntry.source,
        age: 0,
      },
      metadata: {
        totalModels: enhancedModels.length,
        apiModelsAvailable: source === 'api',
        apiError: apiError || undefined,
        lastRefresh: new Date().toISOString(),
      },
    }
  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Failed to fetch models:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch models',
    })
  }
})

// Helper function to fetch models from provider APIs
async function fetchModelsFromAPI(provider: string, apiKey: string): Promise<any[]> {
  switch (provider) {
    case 'openai':
      return await fetchOpenAIModels(apiKey)
    case 'anthropic':
      return await fetchAnthropicModels(apiKey)
    case 'google':
      return await fetchGoogleModels(apiKey)
    case 'xai':
      return await fetchXAIModels(apiKey)
    default:
      throw new Error(`API fetching not supported for provider: ${provider}`)
  }
}

async function fetchOpenAIModels(apiKey: string): Promise<any[]> {
  const response = await fetch('https://api.openai.com/v1/models', {
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  })

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.statusText}`)
  }

  const data = await response.json()
  return data.data?.map((model: any) => ({
    id: model.id,
    name: model.id,
    displayName: model.id,
    contextWindow: getOpenAIContextWindow(model.id),
    capabilities: ['chat'],
    created: model.created,
    ownedBy: model.owned_by,
  })) || []
}

async function fetchAnthropicModels(apiKey: string): Promise<any[]> {
  // Anthropic doesn't have a models endpoint, return static models
  const providerConfig = getLLMProvider('anthropic')
  return providerConfig?.models || []
}

async function fetchGoogleModels(apiKey: string): Promise<any[]> {
  const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`)

  if (!response.ok) {
    throw new Error(`Google API error: ${response.statusText}`)
  }

  const data = await response.json()
  return data.models?.map((model: any) => ({
    id: model.name?.replace('models/', ''),
    name: model.name?.replace('models/', ''),
    displayName: model.displayName || model.name,
    contextWindow: model.inputTokenLimit || 32768,
    capabilities: model.supportedGenerationMethods || ['generateContent'],
    description: model.description,
    version: model.version,
  })) || []
}

async function fetchXAIModels(apiKey: string): Promise<any[]> {
  const response = await fetch('https://api.x.ai/v1/models', {
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  })

  if (!response.ok) {
    throw new Error(`xAI API error: ${response.statusText}`)
  }

  const data = await response.json()
  return data.data?.map((model: any) => ({
    id: model.id,
    name: model.id,
    displayName: model.id,
    contextWindow: 131072, // Default for Grok models
    capabilities: ['chat'],
    created: model.created,
    ownedBy: model.owned_by,
  })) || []
}

function getOpenAIContextWindow(modelId: string): number {
  if (modelId.includes('gpt-4o')) return 128000
  if (modelId.includes('gpt-4-turbo')) return 128000
  if (modelId.includes('gpt-4')) return 8192
  if (modelId.includes('gpt-3.5-turbo')) return 16385
  return 4096 // Default
}
