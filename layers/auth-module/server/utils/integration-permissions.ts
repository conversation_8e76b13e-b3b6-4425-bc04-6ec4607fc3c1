import type { Integration } from '../../types/integration'

/**
 * Permission utilities for multi-level integration management
 */

export interface IntegrationPermissions {
  canRead: boolean
  canWrite: boolean
  canDelete: boolean
  canToggleProfileAvailability: boolean
  canCreateOverride: boolean
  canRemoveOverride: boolean
  canResolveConflicts: boolean
  reason?: string
}

export interface ProfilePermissions {
  canAccessProfile: boolean
  canManageIntegrations: boolean
  canCreateIntegrations: boolean
  canInheritFromUser: boolean
  reason?: string
}

/**
 * Check permissions for a specific integration
 */
export function checkIntegrationPermissions(
  integration: Integration,
  currentUserId: string,
  currentProfileId?: string
): IntegrationPermissions {
  const permissions: IntegrationPermissions = {
    canRead: false,
    canWrite: false,
    canDelete: false,
    canToggleProfileAvailability: false,
    canCreateOverride: false,
    canRemoveOverride: false,
    canResolveConflicts: false,
  }

  // Check basic ownership
  if (integration.userId !== currentUserId) {
    permissions.reason = 'Not the owner of this integration'
    return permissions
  }

  // Basic permissions for owned integrations
  permissions.canRead = true
  permissions.canResolveConflicts = true

  // User-level integration permissions
  if (!integration.profileId) {
    permissions.canWrite = true
    permissions.canDelete = true
    permissions.canToggleProfileAvailability = true
    
    // Can create overrides if integration is available to profiles
    if (integration.availableToProfiles && currentProfileId) {
      permissions.canCreateOverride = true
    }
  }
  // Profile-level integration permissions
  else {
    permissions.canWrite = true
    permissions.canDelete = true
    
    // Can remove override if this is a profile override
    if (integration.profileId === currentProfileId) {
      permissions.canRemoveOverride = true
    }
  }

  return permissions
}

/**
 * Check permissions for profile-level operations
 */
export function checkProfilePermissions(
  profileId: string,
  currentUserId: string,
  userIntegrations: Integration[]
): ProfilePermissions {
  const permissions: ProfilePermissions = {
    canAccessProfile: true, // Assuming user has access to the profile
    canManageIntegrations: true,
    canCreateIntegrations: true,
    canInheritFromUser: false,
  }

  // Check if user has integrations available for inheritance
  const availableForInheritance = userIntegrations.filter(
    integration => !integration.profileId && integration.availableToProfiles && integration.isActive
  )

  if (availableForInheritance.length > 0) {
    permissions.canInheritFromUser = true
  }

  return permissions
}

/**
 * Check if user can perform bulk operations
 */
export function checkBulkOperationPermissions(
  integrations: Integration[],
  currentUserId: string,
  operation: 'delete' | 'activate' | 'deactivate' | 'resolve_conflicts'
): { canPerform: boolean; allowedIntegrations: string[]; reason?: string } {
  const allowedIntegrations: string[] = []

  for (const integration of integrations) {
    const permissions = checkIntegrationPermissions(integration, currentUserId)
    
    switch (operation) {
      case 'delete':
        if (permissions.canDelete) {
          allowedIntegrations.push(integration.id)
        }
        break
      case 'activate':
      case 'deactivate':
        if (permissions.canWrite) {
          allowedIntegrations.push(integration.id)
        }
        break
      case 'resolve_conflicts':
        if (permissions.canResolveConflicts) {
          allowedIntegrations.push(integration.id)
        }
        break
    }
  }

  return {
    canPerform: allowedIntegrations.length > 0,
    allowedIntegrations,
    reason: allowedIntegrations.length === 0 ? 'No integrations eligible for this operation' : undefined,
  }
}

/**
 * Validate integration inheritance rules
 */
export function validateInheritanceRules(
  userIntegration: Integration,
  profileId: string,
  existingProfileIntegrations: Integration[]
): { isValid: boolean; reason?: string } {
  // Check if user integration is available to profiles
  if (!userIntegration.availableToProfiles) {
    return {
      isValid: false,
      reason: 'User integration is not available to profiles',
    }
  }

  // Check if user integration is active
  if (!userIntegration.isActive) {
    return {
      isValid: false,
      reason: 'User integration is not active',
    }
  }

  // Check if profile already has an integration for this provider
  const existingForProvider = existingProfileIntegrations.find(
    integration => integration.provider === userIntegration.provider && integration.isActive
  )

  if (existingForProvider) {
    return {
      isValid: false,
      reason: `Profile already has an active ${userIntegration.provider} integration`,
    }
  }

  return { isValid: true }
}

/**
 * Calculate inheritance efficiency metrics
 */
export function calculateInheritanceMetrics(
  userIntegrations: Integration[],
  profileIntegrations: Integration[]
): {
  totalUserIntegrations: number
  availableToProfiles: number
  profileSpecific: number
  inherited: number
  overridden: number
  inheritanceEfficiency: number
  profileCoverage: number
} {
  const availableToProfiles = userIntegrations.filter(i => i.availableToProfiles && i.isActive).length
  const profileSpecific = profileIntegrations.filter(i => !i.parentIntegrationId).length
  const overridden = profileIntegrations.filter(i => i.parentIntegrationId).length
  
  // Inherited integrations are user integrations that are available to profiles
  // and don't have a profile override
  const overriddenProviders = new Set(
    profileIntegrations
      .filter(i => i.parentIntegrationId)
      .map(i => i.provider)
  )
  
  const inherited = userIntegrations.filter(
    i => i.availableToProfiles && i.isActive && !overriddenProviders.has(i.provider)
  ).length

  const totalEffective = inherited + profileSpecific + overridden
  const inheritanceEfficiency = totalEffective > 0 ? Math.round((inherited / totalEffective) * 100) : 0
  const profileCoverage = userIntegrations.length > 0 ? Math.round((totalEffective / userIntegrations.length) * 100) : 0

  return {
    totalUserIntegrations: userIntegrations.length,
    availableToProfiles,
    profileSpecific,
    inherited,
    overridden,
    inheritanceEfficiency,
    profileCoverage,
  }
}

/**
 * Generate permission summary for UI display
 */
export function generatePermissionSummary(
  integration: Integration,
  currentUserId: string,
  currentProfileId?: string
): {
  level: 'user' | 'profile'
  status: 'active' | 'inactive' | 'inherited' | 'overridden'
  actions: string[]
  restrictions: string[]
} {
  const permissions = checkIntegrationPermissions(integration, currentUserId, currentProfileId)
  const isUserLevel = !integration.profileId
  const isInherited = isUserLevel && integration.availableToProfiles
  const isOverridden = !isUserLevel && !!integration.parentIntegrationId

  const actions: string[] = []
  const restrictions: string[] = []

  if (permissions.canRead) actions.push('View')
  if (permissions.canWrite) actions.push('Edit')
  if (permissions.canDelete) actions.push('Delete')
  if (permissions.canToggleProfileAvailability) actions.push('Share with Profiles')
  if (permissions.canCreateOverride) actions.push('Create Override')
  if (permissions.canRemoveOverride) actions.push('Remove Override')

  if (!permissions.canWrite) restrictions.push('Cannot edit')
  if (!permissions.canDelete) restrictions.push('Cannot delete')
  if (!permissions.canToggleProfileAvailability) restrictions.push('Cannot change profile sharing')

  let status: 'active' | 'inactive' | 'inherited' | 'overridden' = integration.isActive ? 'active' : 'inactive'
  if (isInherited) status = 'inherited'
  if (isOverridden) status = 'overridden'

  return {
    level: isUserLevel ? 'user' : 'profile',
    status,
    actions,
    restrictions,
  }
}
