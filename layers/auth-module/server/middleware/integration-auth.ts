import { createError, define<PERSON><PERSON><PERSON><PERSON><PERSON>, getR<PERSON>er<PERSON>aram } from 'h3'
import { doc, getDoc } from 'firebase/firestore'
import { useFirebaseServer } from '../firebase/init'
import { getUserSession } from '../utils/session'

/**
 * Authentication and authorization middleware for integration endpoints
 * Handles multi-level access control for user and profile integrations
 */

export interface AuthContext {
  user: {
    uid: string
    email?: string
  }
  integration?: {
    id: string
    userId: string
    profileId?: string
    provider: string
    isActive: boolean
  }
  permissions: {
    canRead: boolean
    canWrite: boolean
    canDelete: boolean
    canManageProfiles: boolean
  }
}

/**
 * Check if user has access to a specific integration
 */
export async function checkIntegrationAccess(
  integrationId: string,
  userId: string,
  requiredPermission: 'read' | 'write' | 'delete',
  firestore: any
): Promise<{ hasAccess: boolean; integration?: any; reason?: string }> {
  try {
    // Get the integration
    const integrationRef = doc(firestore, 'integrations', integrationId)
    const integrationSnap = await getDoc(integrationRef)

    if (!integrationSnap.exists()) {
      return { hasAccess: false, reason: 'Integration not found' }
    }

    const integration = integrationSnap.data()

    // Check basic ownership
    if (integration.userId !== userId) {
      return { hasAccess: false, reason: 'Not the owner of this integration' }
    }

    // Check if integration is active for write/delete operations
    if ((requiredPermission === 'write' || requiredPermission === 'delete') && !integration.isActive) {
      return { hasAccess: false, reason: 'Cannot modify inactive integration' }
    }

    return { hasAccess: true, integration }
  } catch (error) {
    console.error('Error checking integration access:', error)
    return { hasAccess: false, reason: 'Access check failed' }
  }
}

/**
 * Check if user has access to a specific profile
 */
export async function checkProfileAccess(
  profileId: string,
  userId: string,
  firestore: any
): Promise<{ hasAccess: boolean; profile?: any; reason?: string }> {
  try {
    // Get the profile
    const profileRef = doc(firestore, 'profiles', profileId)
    const profileSnap = await getDoc(profileRef)

    if (!profileSnap.exists()) {
      return { hasAccess: false, reason: 'Profile not found' }
    }

    const profile = profileSnap.data()

    // Check if user owns the profile or has access to it
    if (profile.userId !== userId && !profile.members?.includes(userId)) {
      return { hasAccess: false, reason: 'No access to this profile' }
    }

    return { hasAccess: true, profile }
  } catch (error) {
    console.error('Error checking profile access:', error)
    return { hasAccess: false, reason: 'Profile access check failed' }
  }
}

/**
 * Middleware for integration endpoints
 */
export default defineEventHandler(async (event) => {
  // Only apply to integration API routes
  if (!event.node.req.url?.startsWith('/api/integrations')) {
    return
  }

  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session?.user?.uid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Authentication required',
      })
    }

    // Initialize Firebase
    const { firestore } = await useFirebaseServer(session.user.token?.idToken as string)

    // Get route parameters
    const integrationId = getRouterParam(event, 'id')
    const profileId = getRouterParam(event, 'profileId')
    const userId = getRouterParam(event, 'userId')

    // Build auth context
    const authContext: AuthContext = {
      user: {
        uid: session.user.uid,
        email: session.user.email,
      },
      permissions: {
        canRead: true, // Basic read permission for authenticated users
        canWrite: true, // Can write their own integrations
        canDelete: true, // Can delete their own integrations
        canManageProfiles: true, // Can manage their own profiles
      },
    }

    // Check specific integration access if integration ID is provided
    if (integrationId) {
      const method = event.node.req.method?.toUpperCase()
      let requiredPermission: 'read' | 'write' | 'delete' = 'read'

      if (method === 'PATCH' || method === 'PUT' || method === 'POST') {
        requiredPermission = 'write'
      } else if (method === 'DELETE') {
        requiredPermission = 'delete'
      }

      const accessCheck = await checkIntegrationAccess(
        integrationId,
        session.user.uid,
        requiredPermission,
        firestore
      )

      if (!accessCheck.hasAccess) {
        throw createError({
          statusCode: 403,
          statusMessage: `Forbidden: ${accessCheck.reason}`,
        })
      }

      authContext.integration = {
        id: integrationId,
        userId: accessCheck.integration.userId,
        profileId: accessCheck.integration.profileId,
        provider: accessCheck.integration.provider,
        isActive: accessCheck.integration.isActive,
      }
    }

    // Check profile access if profile ID is provided
    if (profileId && profileId !== 'user-level') {
      const profileAccessCheck = await checkProfileAccess(
        profileId,
        session.user.uid,
        firestore
      )

      if (!profileAccessCheck.hasAccess) {
        throw createError({
          statusCode: 403,
          statusMessage: `Forbidden: ${profileAccessCheck.reason}`,
        })
      }
    }

    // Check user ID access if provided (users can only access their own data)
    if (userId && userId !== session.user.uid) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden: Cannot access other users\' data',
      })
    }

    // Add auth context to event for use in handlers
    event.context.auth = authContext

  } catch (error) {
    // Re-throw HTTP errors
    if ((error as any).statusCode) {
      throw error
    }

    // Log unexpected errors
    console.error('Authentication middleware error:', error)

    // Return generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Authentication check failed',
    })
  }
})
