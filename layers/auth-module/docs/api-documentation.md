# Multi-Level Integration Management API Documentation

## Overview

This API provides comprehensive endpoints for managing LLM integrations across user and profile levels with inheritance, conflict resolution, and advanced features.

## Authentication

All endpoints require authentication via Firebase ID token. Include the token in the Authorization header:

```
Authorization: Bearer <firebase-id-token>
```

## Base URL

```
/api/integrations
```

## Core Integration CRUD Endpoints

### 1. List Integrations

**GET** `/api/integrations`

Get integrations with filtering and pagination.

**Query Parameters:**
- `userId` (string): Filter by user ID (defaults to current user)
- `profileId` (string): Filter by profile ID (`null` for user-level, `user-level` for user-level only)
- `provider` (string): Filter by provider
- `isActive` (boolean): Filter by active status
- `availableToProfiles` (boolean): Filter by profile availability
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 50, max: 100)
- `orderBy` (string): Sort field (default: createdAt)
- `orderDirection` (string): Sort direction (asc/desc, default: desc)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "integration-id",
      "userId": "user-id",
      "profileId": "profile-id",
      "provider": "openai",
      "name": "My OpenAI Integration",
      "description": "Primary OpenAI integration",
      "credentials": {
        "apiKey": "***",
        "encryptedAt": "timestamp"
      },
      "settings": {
        "defaultModel": "gpt-4-turbo",
        "maxTokens": 4096
      },
      "isActive": true,
      "isDefault": false,
      "availableToProfiles": true,
      "lastUsedAt": "timestamp",
      "createdAt": "timestamp",
      "updatedAt": "timestamp"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 100,
    "totalPages": 2,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### 2. Create Integration

**POST** `/api/integrations`

Create a new integration (user-level or profile-level).

**Request Body:**
```json
{
  "name": "My OpenAI Integration",
  "provider": "openai",
  "apiKey": "sk-...",
  "description": "Primary OpenAI integration",
  "profileId": "profile-id", // Optional: for profile-level integrations
  "parentIntegrationId": "parent-id", // Optional: for profile overrides
  "settings": {
    "defaultModel": "gpt-4-turbo",
    "maxTokens": 4096,
    "temperature": 0.7
  },
  "isDefault": false,
  "availableToProfiles": true
}
```

**Response:**
```json
{
  "success": true,
  "integration": { /* Integration object */ },
  "message": "openai integration created successfully"
}
```

### 3. Get Integration

**GET** `/api/integrations/{id}`

Get a specific integration by ID.

**Response:**
```json
{
  "success": true,
  "integration": { /* Integration object */ }
}
```

### 4. Update Integration

**PATCH** `/api/integrations/{id}`

Update an existing integration.

**Request Body:**
```json
{
  "name": "Updated Name",
  "description": "Updated description",
  "isActive": true,
  "isDefault": false,
  "availableToProfiles": true,
  "settings": {
    "defaultModel": "gpt-4o",
    "maxTokens": 8192
  },
  "apiKey": "new-api-key", // Optional: to update API key
  "markAsUsed": true // Optional: update lastUsedAt timestamp
}
```

**Response:**
```json
{
  "success": true,
  "integration": { /* Updated integration object */ },
  "message": "Integration updated successfully"
}
```

### 5. Delete Integration

**DELETE** `/api/integrations/{id}`

Delete an integration.

**Query Parameters:**
- `force` (boolean): Force delete even if it has dependent profile integrations

**Response:**
```json
{
  "success": true,
  "message": "Integration deleted successfully",
  "deletedId": "integration-id"
}
```

## User-Level Integration Endpoints

### 6. Get User Integrations

**GET** `/api/integrations/user/{userId}`

Get all integrations for a specific user.

**Query Parameters:**
- `includeProfileIntegrations` (boolean): Include profile-level integrations (default: false)
- `provider` (string): Filter by provider
- `isActive` (boolean): Filter by active status

**Response:**
```json
{
  "success": true,
  "data": {
    "userLevelIntegrations": [ /* Array of user-level integrations */ ],
    "profileIntegrationsByProfile": { /* Profile integrations grouped by profile ID */ },
    "allIntegrations": [ /* All integrations if includeProfileIntegrations=true */ ]
  },
  "summary": {
    "totalIntegrations": 5,
    "userLevelIntegrations": 3,
    "profileLevelIntegrations": 2,
    "availableToProfiles": 2,
    "activeIntegrations": 4,
    "providerCounts": {
      "openai": 2,
      "anthropic": 1
    },
    "profileCount": 2
  }
}
```

## Profile-Level Integration Endpoints

### 7. Get Profile Integrations

**GET** `/api/integrations/profile/{profileId}`

Get all integrations for a specific profile, including inherited and profile-specific.

**Query Parameters:**
- `includeInherited` (boolean): Include inherited user-level integrations (default: true)
- `provider` (string): Filter by provider
- `isActive` (boolean): Filter by active status

**Response:**
```json
{
  "success": true,
  "data": {
    "profileSpecificIntegrations": [ /* Profile-specific integrations */ ],
    "inheritedIntegrations": [ /* Inherited from user-level */ ],
    "effectiveIntegrations": [ /* All effective integrations */ ],
    "allIntegrations": [ /* All integrations */ ]
  },
  "conflicts": [
    {
      "provider": "openai",
      "integrations": [ /* Conflicting integrations */ ],
      "type": "duplicate_provider"
    }
  ],
  "summary": {
    "totalIntegrations": 4,
    "profileSpecificIntegrations": 1,
    "inheritedIntegrations": 2,
    "effectiveIntegrations": 3,
    "conflicts": 1,
    "inheritanceEfficiency": 67
  }
}
```

### 8. Create Profile Override

**POST** `/api/integrations/profile/{profileId}/override`

Create a profile override for an inherited user-level integration.

**Request Body:**
```json
{
  "parentIntegrationId": "user-integration-id",
  "name": "OpenAI (Profile Override)",
  "provider": "openai",
  "apiKey": "profile-specific-api-key",
  "description": "Profile-specific OpenAI integration",
  "settings": {
    "defaultModel": "gpt-4o",
    "maxTokens": 8192
  }
}
```

**Response:**
```json
{
  "success": true,
  "integration": { /* Created override integration */ },
  "parentIntegration": {
    "id": "parent-id",
    "name": "Parent Integration Name",
    "provider": "openai"
  },
  "message": "Profile override for openai created successfully"
}
```

### 9. Remove Profile Override

**DELETE** `/api/integrations/profile/{profileId}/override/{id}`

Remove a profile override and revert to inherited integration.

**Response:**
```json
{
  "success": true,
  "message": "Profile override removed successfully",
  "deletedOverride": {
    "id": "override-id",
    "name": "Override Name",
    "provider": "openai",
    "profileId": "profile-id"
  },
  "revertedToParent": {
    "id": "parent-id",
    "name": "Parent Integration",
    "provider": "openai",
    "isActive": true,
    "availableToProfiles": true
  }
}
```

## Conflict Resolution Endpoints

### 10. Detect Conflicts

**GET** `/api/integrations/conflicts`

Detect integration conflicts for the current user.

**Query Parameters:**
- `profileId` (string): Check conflicts for specific profile
- `provider` (string): Check conflicts for specific provider
- `severity` (string): Filter by conflict severity (warning/error)

**Response:**
```json
{
  "success": true,
  "conflicts": [
    {
      "integration": { /* Integration object */ },
      "conflict": {
        "type": "duplicate_provider",
        "message": "Multiple active integrations for openai",
        "severity": "warning"
      },
      "status": {
        "isProfileSpecific": true,
        "isInherited": false,
        "isOverridden": true,
        "isEffective": true,
        "source": "profile",
        "parentIntegration": { /* Parent integration if applicable */ }
      }
    }
  ],
  "summary": {
    "totalConflicts": 2,
    "conflictsByType": {
      "duplicate_provider": { "count": 1, "severity": "warning" },
      "deprecated_parent": { "count": 1, "severity": "error" }
    },
    "hasErrors": true,
    "hasWarnings": true
  }
}
```

### 11. Resolve Conflicts

**POST** `/api/integrations/conflicts/resolve`

Resolve integration conflicts with specified strategies.

**Request Body:**
```json
{
  "resolutions": [
    {
      "integrationId": "integration-id",
      "resolution": "keep_profile"
    }
  ],
  "autoResolve": false
}
```

**Resolution Strategies:**
- `keep_profile`: Keep profile integration, deactivate user integration
- `keep_user`: Keep user integration, remove profile override
- `keep_newest`: Keep the most recently created integration
- `create_independent`: Convert profile override to independent integration
- `deactivate`: Deactivate the integration
- `merge_settings`: Merge settings from parent integration

**Response:**
```json
{
  "success": true,
  "results": [
    {
      "integrationId": "integration-id",
      "action": "kept_profile_deactivated_user",
      "success": true
    }
  ],
  "summary": {
    "totalResolutions": 1,
    "successful": 1,
    "failed": 0,
    "actions": {
      "kept_profile_deactivated_user": 1
    }
  },
  "message": "Resolved 1/1 conflicts"
}
```

## Enhanced Model Fetching Endpoints

### 12. Get Provider Models

**GET** `/api/integrations/models/{provider}`

Get models for a specific provider with enhanced caching and validation.

**Query Parameters:**
- `refresh` (boolean): Force refresh from API (default: false)
- `includeStatic` (boolean): Include static model definitions (default: true)
- `apiKey` (string): Use specific API key for fetching (optional)

**Response:**
```json
{
  "success": true,
  "provider": {
    "id": "openai",
    "name": "OpenAI",
    "icon": "simple-icons:openai",
    "description": "OpenAI GPT models",
    "apiEndpoint": "https://api.openai.com/v1",
    "requiresApiKey": true
  },
  "models": [
    {
      "id": "gpt-4-turbo",
      "name": "gpt-4-turbo",
      "displayName": "GPT-4 Turbo",
      "contextWindow": 128000,
      "capabilities": ["chat"],
      "provider": "openai",
      "providerName": "OpenAI",
      "isApiModel": true,
      "lastUpdated": "2024-01-01T00:00:00Z",
      "pricing": {
        "input": 0.01,
        "output": 0.03
      }
    }
  ],
  "cache": {
    "hit": false,
    "timestamp": 1704067200000,
    "source": "api",
    "age": 0
  },
  "metadata": {
    "totalModels": 15,
    "apiModelsAvailable": true,
    "lastRefresh": "2024-01-01T00:00:00Z"
  }
}
```

## Testing and Validation Endpoints

### 13. Test Integration

**POST** `/api/integrations/test`

Test an integration's connectivity and functionality.

**Request Body:**
```json
{
  "integrationId": "integration-id",
  "testType": "simple_chat", // connectivity | model_list | simple_chat | full_test
  "model": "gpt-4-turbo", // Optional: specific model to test
  "message": "Hello, this is a test message." // Optional: custom test message
}
```

**Response:**
```json
{
  "success": true,
  "testResult": {
    "success": true,
    "latency": 1250,
    "model": "gpt-4-turbo",
    "response": "Hello! I'm working correctly.",
    "metadata": {
      "tokensUsed": 25,
      "provider": "openai",
      "timestamp": "2024-01-01T00:00:00Z"
    }
  },
  "integration": {
    "id": "integration-id",
    "name": "My OpenAI Integration",
    "provider": "openai",
    "model": "gpt-4-turbo"
  },
  "testType": "simple_chat"
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "statusCode": 400,
  "statusMessage": "Bad Request",
  "message": "Detailed error message"
}
```

**Common Status Codes:**
- `400`: Bad Request - Invalid parameters or request body
- `401`: Unauthorized - Authentication required
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - Resource not found
- `409`: Conflict - Resource conflict (e.g., duplicate integration)
- `500`: Internal Server Error - Server-side error

## Rate Limiting

API endpoints are rate-limited to prevent abuse:
- **General endpoints**: 100 requests per minute per user
- **Test endpoints**: 10 requests per minute per user
- **Model fetching**: 20 requests per minute per user

## Webhooks (Future Enhancement)

Webhook support for integration events:
- `integration.created`
- `integration.updated`
- `integration.deleted`
- `conflict.detected`
- `conflict.resolved`

## SDK and Client Libraries

Official client libraries available for:
- JavaScript/TypeScript (npm: `@your-org/integration-api`)
- Python (pip: `your-org-integration-api`)
- Go (go get: `github.com/your-org/integration-api-go`)

## Support

For API support and questions:
- Documentation: `/docs/api`
- Support Email: <EMAIL>
- GitHub Issues: `https://github.com/your-org/integration-api/issues`
