import type { LLMProviderConfig, LLMModel } from '../types/integration'

export const LLM_PROVIDERS: Record<string, LLMProviderConfig> = {
  openai: {
    id: 'openai',
    name: 'OpenAI',
    icon: 'logos:openai-icon',
    description: 'Access GPT-4, GPT-4 Turbo, GPT-3.5 and other OpenAI models',
    requiredCredentials: ['apiKey'],
    endpoints: {
      base: 'https://api.openai.com/v1',
      chat: '/chat/completions',
      embeddings: '/embeddings',
      models: '/models',
    },
    headers: {
      'Authorization': 'Bearer {apiKey}',
      'Content-Type': 'application/json',
    },
    models: [
      {
        id: 'gpt-4-turbo-preview',
        name: 'gpt-4-turbo-preview',
        displayName: 'GPT-4 Turbo (Preview)',
        contextWindow: 128000,
        capabilities: ['chat', 'function-calling', 'vision'],
        pricing: { input: 10, output: 30, currency: 'USD' },
        knowledgeCutoff: '2023-04',
      },
      {
        id: 'gpt-4-turbo',
        name: 'gpt-4-turbo',
        displayName: 'GPT-4 Turbo',
        contextWindow: 128000,
        capabilities: ['chat', 'function-calling', 'vision'],
        pricing: { input: 10, output: 30, currency: 'USD' },
        knowledgeCutoff: '2023-04',
      },
      {
        id: 'gpt-4',
        name: 'gpt-4',
        displayName: 'GPT-4',
        contextWindow: 8192,
        capabilities: ['chat', 'function-calling'],
        pricing: { input: 30, output: 60, currency: 'USD' },
        knowledgeCutoff: '2021-09',
      },
      {
        id: 'gpt-4-32k',
        name: 'gpt-4-32k',
        displayName: 'GPT-4 (32K)',
        contextWindow: 32768,
        capabilities: ['chat', 'function-calling'],
        pricing: { input: 60, output: 120, currency: 'USD' },
        knowledgeCutoff: '2021-09',
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'gpt-3.5-turbo',
        displayName: 'GPT-3.5 Turbo',
        contextWindow: 16384,
        capabilities: ['chat', 'function-calling'],
        pricing: { input: 0.5, output: 1.5, currency: 'USD' },
        knowledgeCutoff: '2021-09',
      },
      {
        id: 'gpt-3.5-turbo-1106',
        name: 'gpt-3.5-turbo-1106',
        displayName: 'GPT-3.5 Turbo (Latest)',
        contextWindow: 16384,
        capabilities: ['chat', 'function-calling'],
        pricing: { input: 1, output: 2, currency: 'USD' },
        knowledgeCutoff: '2021-09',
      },
    ],
  },
  anthropic: {
    id: 'anthropic',
    name: 'Anthropic',
    icon: 'simple-icons:anthropic',
    description: 'Access Claude 3.5 Sonnet, Claude 3.5 Haiku, and other Anthropic models',
    requiredCredentials: ['apiKey'],
    endpoints: {
      base: 'https://api.anthropic.com/v1',
      chat: '/messages',
      models: '/models',
    },
    headers: {
      'x-api-key': '{apiKey}',
      'anthropic-version': '2023-06-01',
      'Content-Type': 'application/json',
    },
    models: [
      {
        id: 'claude-3-5-sonnet-20241022',
        name: 'claude-3-5-sonnet-20241022',
        displayName: 'Claude 3.5 Sonnet (Oct 2024)',
        contextWindow: 200000,
        capabilities: ['chat', 'vision', 'function-calling'],
        pricing: { input: 3, output: 15, currency: 'USD' },
        knowledgeCutoff: '2024-04',
      },
      {
        id: 'claude-3-5-haiku-20241022',
        name: 'claude-3-5-haiku-20241022',
        displayName: 'Claude 3.5 Haiku',
        contextWindow: 200000,
        capabilities: ['chat', 'vision'],
        pricing: { input: 0.8, output: 4, currency: 'USD' },
        knowledgeCutoff: '2024-07',
      },
      {
        id: 'claude-3-opus-20240229',
        name: 'claude-3-opus-20240229',
        displayName: 'Claude 3 Opus',
        contextWindow: 200000,
        capabilities: ['chat', 'vision'],
        pricing: { input: 15, output: 75, currency: 'USD' },
        knowledgeCutoff: '2023-08',
      },
      {
        id: 'claude-3-sonnet-20240229',
        name: 'claude-3-sonnet-20240229',
        displayName: 'Claude 3 Sonnet',
        contextWindow: 200000,
        capabilities: ['chat', 'vision'],
        pricing: { input: 3, output: 15, currency: 'USD' },
        knowledgeCutoff: '2023-08',
      },
      {
        id: 'claude-3-haiku-20240307',
        name: 'claude-3-haiku-20240307',
        displayName: 'Claude 3 Haiku',
        contextWindow: 200000,
        capabilities: ['chat', 'vision'],
        pricing: { input: 0.25, output: 1.25, currency: 'USD' },
        knowledgeCutoff: '2023-08',
      },
    ],
  },
  google: {
    id: 'google',
    name: 'Google AI',
    icon: 'logos:google-icon',
    description: 'Access Gemini 2.0, Gemini 1.5 Pro, and other Google AI models',
    requiredCredentials: ['apiKey'],
    endpoints: {
      base: 'https://generativelanguage.googleapis.com/v1beta',
      chat: '/models/{model}:generateContent',
      models: '/models',
    },
    headers: {
      'Content-Type': 'application/json',
    },
    models: [
      {
        id: 'gemini-2.0-flash-exp',
        name: 'gemini-2.0-flash-exp',
        displayName: 'Gemini 2.0 Flash (Experimental)',
        contextWindow: 1000000,
        capabilities: ['chat', 'vision', 'function-calling', 'multimodal'],
        pricing: { input: 0, output: 0, currency: 'USD' }, // Free during experimental
        releaseDate: '2024-12',
      },
      {
        id: 'gemini-1.5-pro',
        name: 'gemini-1.5-pro',
        displayName: 'Gemini 1.5 Pro',
        contextWindow: 1000000,
        capabilities: ['chat', 'vision', 'function-calling', 'multimodal'],
        pricing: { input: 3.5, output: 10.5, currency: 'USD' },
        knowledgeCutoff: '2024-04',
      },
      {
        id: 'gemini-1.5-pro-002',
        name: 'gemini-1.5-pro-002',
        displayName: 'Gemini 1.5 Pro (Latest)',
        contextWindow: 2000000,
        capabilities: ['chat', 'vision', 'function-calling', 'multimodal'],
        pricing: { input: 3.5, output: 10.5, currency: 'USD' },
        knowledgeCutoff: '2024-11',
      },
      {
        id: 'gemini-1.5-flash',
        name: 'gemini-1.5-flash',
        displayName: 'Gemini 1.5 Flash',
        contextWindow: 1000000,
        capabilities: ['chat', 'vision', 'function-calling', 'multimodal'],
        pricing: { input: 0.075, output: 0.3, currency: 'USD' },
        knowledgeCutoff: '2024-04',
      },
      {
        id: 'gemini-1.5-flash-002',
        name: 'gemini-1.5-flash-002',
        displayName: 'Gemini 1.5 Flash (Latest)',
        contextWindow: 1000000,
        capabilities: ['chat', 'vision', 'function-calling', 'multimodal'],
        pricing: { input: 0.075, output: 0.3, currency: 'USD' },
        knowledgeCutoff: '2024-11',
      },
    ],
  },
  xai: {
    id: 'xai',
    name: 'xAI',
    icon: 'simple-icons:x',
    description: 'Access Grok-3, Grok-2, and other xAI models',
    requiredCredentials: ['apiKey'],
    endpoints: {
      base: 'https://api.x.ai/v1',
      chat: '/chat/completions',
      models: '/models',
    },
    headers: {
      'Authorization': 'Bearer {apiKey}',
      'Content-Type': 'application/json',
    },
    models: [
      {
        id: 'grok-beta',
        name: 'grok-beta',
        displayName: 'Grok (Beta)',
        contextWindow: 131072,
        capabilities: ['chat', 'function-calling'],
        pricing: { input: 5, output: 15, currency: 'USD' },
        releaseDate: '2024-12',
      },
      {
        id: 'grok-2',
        name: 'grok-2',
        displayName: 'Grok 2',
        contextWindow: 131072,
        capabilities: ['chat', 'vision', 'function-calling'],
        pricing: { input: 10, output: 30, currency: 'USD' },
        releaseDate: '2024-08',
      },
      {
        id: 'grok-2-mini',
        name: 'grok-2-mini',
        displayName: 'Grok 2 Mini',
        contextWindow: 131072,
        capabilities: ['chat', 'function-calling'],
        pricing: { input: 2, output: 10, currency: 'USD' },
        releaseDate: '2024-08',
      },
    ],
  },
}

// Helper function to get provider by ID
export function getLLMProvider(providerId: string): LLMProviderConfig | undefined {
  return LLM_PROVIDERS[providerId]
}

// Helper function to get all providers as array
export function getAllLLMProviders(): LLMProviderConfig[] {
  return Object.values(LLM_PROVIDERS)
}

// Helper function to get models for a provider
export function getProviderModels(providerId: string): LLMModel[] {
  const provider = getLLMProvider(providerId)
  return provider?.models || []
}

// Helper function to get default model for a provider
export function getDefaultModel(providerId: string): LLMModel | undefined {
  const models = getProviderModels(providerId)
  return models[0] // Return first model as default
}

// Helper function to check if provider supports dynamic model fetching
export function supportsModelFetching(providerId: string): boolean {
  const provider = getLLMProvider(providerId)
  return !!(provider?.endpoints?.models)
}

// Helper function to get model fetching endpoint for a provider
export function getModelFetchingEndpoint(providerId: string): string | undefined {
  const provider = getLLMProvider(providerId)
  if (!provider?.endpoints?.models) return undefined

  return `${provider.endpoints.base}${provider.endpoints.models}`
}

// Helper function to get authentication headers for a provider
export function getAuthHeaders(providerId: string, apiKey: string): Record<string, string> {
  const provider = getLLMProvider(providerId)
  if (!provider?.headers) return {}

  const headers: Record<string, string> = {}

  Object.entries(provider.headers).forEach(([key, value]) => {
    if (typeof value === 'string') {
      headers[key] = value.replace('{apiKey}', apiKey)
    }
  })

  return headers
}

// Helper function to merge dynamic models with static fallbacks
export function mergeModels(
  staticModels: LLMModel[],
  dynamicModels: LLMModel[]
): LLMModel[] {
  if (dynamicModels.length === 0) {
    return staticModels
  }

  // Create a map of dynamic models by ID for quick lookup
  const dynamicModelMap = new Map(dynamicModels.map(model => [model.id, model]))

  // Start with dynamic models
  const mergedModels = [...dynamicModels]

  // Add static models that aren't in dynamic list
  staticModels.forEach(staticModel => {
    if (!dynamicModelMap.has(staticModel.id)) {
      mergedModels.push({
        ...staticModel,
        deprecated: true, // Mark static-only models as potentially deprecated
      })
    }
  })

  // Sort by release date (newest first) and then by name
  return mergedModels.sort((a, b) => {
    if (a.releaseDate && b.releaseDate) {
      return b.releaseDate.localeCompare(a.releaseDate)
    }
    if (a.releaseDate && !b.releaseDate) return -1
    if (!a.releaseDate && b.releaseDate) return 1
    return a.displayName.localeCompare(b.displayName)
  })
}
