import type { LLMProvider, <PERSON><PERSON>ode<PERSON>, Model<PERSON><PERSON>, ModelFetchResult, ModelFetchOptions } from '../types/integration'
import { modelFetcher } from '../services/modelFetcher'
import { getLLMProvider } from '../config/llm-providers'

/**
 * Composable for managing model cache with 24-hour TTL
 * Stores cache in localStorage for persistence across sessions
 */
export function useModelCache() {
  // Cache configuration
  const CACHE_TTL = 24 * 60 * 60 * 1000 // 24 hours in milliseconds
  const CACHE_KEY_PREFIX = 'llm_models_cache_'
  const BACKGROUND_REFRESH_THRESHOLD = 2 * 60 * 60 * 1000 // 2 hours

  // Reactive state
  const loading = ref<Record<LLMProvider, boolean>>({
    openai: false,
    anthropic: false,
    google: false,
    xai: false,
  })

  const lastUpdated = ref<Record<LLMProvider, number | null>>({
    openai: null,
    anthropic: null,
    google: null,
    xai: null,
  })

  /**
   * Get cache key for a provider
   */
  const getCacheKey = (provider: LLMProvider): string => {
    return `${CACHE_KEY_PREFIX}${provider}`
  }

  /**
   * Check if cache exists and is valid for a provider
   */
  const isCacheValid = (provider: LLMProvider): boolean => {
    if (process.server) return false

    try {
      const cached = localStorage.getItem(getCacheKey(provider))
      if (!cached) return false

      const cache: ModelCache = JSON.parse(cached)
      const now = Date.now()
      
      return cache.expiresAt > now && cache.models.length > 0
    } catch (error) {
      console.warn(`Error checking cache for ${provider}:`, error)
      return false
    }
  }

  /**
   * Get cached models for a provider
   */
  const getCachedModels = (provider: LLMProvider): LLMModel[] | null => {
    if (process.server) return null

    try {
      const cached = localStorage.getItem(getCacheKey(provider))
      if (!cached) return null

      const cache: ModelCache = JSON.parse(cached)
      const now = Date.now()

      if (cache.expiresAt > now) {
        lastUpdated.value[provider] = cache.lastFetched
        return cache.models
      }

      // Cache expired, remove it
      localStorage.removeItem(getCacheKey(provider))
      return null
    } catch (error) {
      console.warn(`Error reading cache for ${provider}:`, error)
      return null
    }
  }

  /**
   * Store models in cache
   */
  const setCachedModels = (provider: LLMProvider, models: LLMModel[]): void => {
    if (process.server) return

    try {
      const now = Date.now()
      const cache: ModelCache = {
        provider,
        models,
        lastFetched: now,
        expiresAt: now + CACHE_TTL,
      }

      localStorage.setItem(getCacheKey(provider), JSON.stringify(cache))
      lastUpdated.value[provider] = now
    } catch (error) {
      console.warn(`Error storing cache for ${provider}:`, error)
    }
  }

  /**
   * Clear cache for a specific provider
   */
  const clearCache = (provider: LLMProvider): void => {
    if (process.server) return

    try {
      localStorage.removeItem(getCacheKey(provider))
      lastUpdated.value[provider] = null
    } catch (error) {
      console.warn(`Error clearing cache for ${provider}:`, error)
    }
  }

  /**
   * Clear all model caches
   */
  const clearAllCaches = (): void => {
    if (process.server) return

    const providers: LLMProvider[] = ['openai', 'anthropic', 'google', 'xai']
    providers.forEach(provider => clearCache(provider))
  }

  /**
   * Get models for a provider (from cache or API)
   */
  const getModels = async (
    provider: LLMProvider,
    apiKey?: string,
    options: ModelFetchOptions = {}
  ): Promise<ModelFetchResult> => {
    const { forceRefresh = false, fallbackToStatic = true } = options

    // Check if we should use cache
    if (!forceRefresh && isCacheValid(provider)) {
      const cachedModels = getCachedModels(provider)
      if (cachedModels) {
        // Check if we should refresh in background
        const cache = JSON.parse(localStorage.getItem(getCacheKey(provider)) || '{}')
        const shouldBackgroundRefresh = 
          Date.now() - cache.lastFetched > BACKGROUND_REFRESH_THRESHOLD

        if (shouldBackgroundRefresh && apiKey) {
          // Refresh in background without blocking
          refreshModelsInBackground(provider, apiKey)
        }

        return {
          success: true,
          models: cachedModels,
          fromCache: true,
          lastUpdated: cache.lastFetched,
        }
      }
    }

    // Fetch from API if no valid cache or force refresh
    if (apiKey) {
      return await fetchAndCacheModels(provider, apiKey, options)
    }

    // Fallback to static models if no API key
    if (fallbackToStatic) {
      const providerConfig = getLLMProvider(provider)
      return {
        success: true,
        models: providerConfig?.models || [],
        fromCache: false,
        error: 'No API key provided, using static models',
      }
    }

    return {
      success: false,
      models: [],
      error: 'No API key provided and fallback disabled',
      fromCache: false,
    }
  }

  /**
   * Fetch models from API and cache them
   */
  const fetchAndCacheModels = async (
    provider: LLMProvider,
    apiKey: string,
    options: ModelFetchOptions = {}
  ): Promise<ModelFetchResult> => {
    loading.value[provider] = true

    try {
      const result = await modelFetcher.fetchModels(provider, apiKey, options)
      
      if (result.success && result.models.length > 0) {
        setCachedModels(provider, result.models)
      }

      return result
    } finally {
      loading.value[provider] = false
    }
  }

  /**
   * Refresh models in background without blocking UI
   */
  const refreshModelsInBackground = async (
    provider: LLMProvider,
    apiKey: string
  ): Promise<void> => {
    try {
      const result = await modelFetcher.fetchModels(provider, apiKey, {
        fallbackToStatic: false,
        timeout: 5000, // Shorter timeout for background refresh
      })

      if (result.success && result.models.length > 0) {
        setCachedModels(provider, result.models)
      }
    } catch (error) {
      // Silently fail background refresh
      console.debug(`Background refresh failed for ${provider}:`, error)
    }
  }

  /**
   * Refresh cache for a specific provider
   */
  const refreshCache = async (
    provider: LLMProvider,
    apiKey: string,
    options: ModelFetchOptions = {}
  ): Promise<ModelFetchResult> => {
    return await fetchAndCacheModels(provider, apiKey, { ...options, forceRefresh: true })
  }

  /**
   * Refresh all provider caches
   */
  const refreshAllCaches = async (
    apiKeys: Partial<Record<LLMProvider, string>>
  ): Promise<Record<LLMProvider, ModelFetchResult>> => {
    const results: Partial<Record<LLMProvider, ModelFetchResult>> = {}
    const providers: LLMProvider[] = ['openai', 'anthropic', 'google', 'xai']

    await Promise.allSettled(
      providers.map(async (provider) => {
        const apiKey = apiKeys[provider]
        if (apiKey) {
          results[provider] = await refreshCache(provider, apiKey)
        }
      })
    )

    return results as Record<LLMProvider, ModelFetchResult>
  }

  /**
   * Get cache status for all providers
   */
  const getCacheStatus = (): Record<LLMProvider, {
    hasCache: boolean
    isValid: boolean
    lastFetched: number | null
    expiresAt: number | null
    modelCount: number
  }> => {
    const providers: LLMProvider[] = ['openai', 'anthropic', 'google', 'xai']
    const status: any = {}

    providers.forEach(provider => {
      const hasCache = !!getCachedModels(provider)
      const isValid = isCacheValid(provider)
      let lastFetched = null
      let expiresAt = null
      let modelCount = 0

      if (hasCache) {
        try {
          const cached = localStorage.getItem(getCacheKey(provider))
          if (cached) {
            const cache: ModelCache = JSON.parse(cached)
            lastFetched = cache.lastFetched
            expiresAt = cache.expiresAt
            modelCount = cache.models.length
          }
        } catch (error) {
          // Ignore errors
        }
      }

      status[provider] = {
        hasCache,
        isValid,
        lastFetched,
        expiresAt,
        modelCount,
      }
    })

    return status
  }

  /**
   * Initialize cache status on mount
   */
  onMounted(() => {
    if (process.client) {
      const providers: LLMProvider[] = ['openai', 'anthropic', 'google', 'xai']
      providers.forEach(provider => {
        const cached = getCachedModels(provider)
        if (cached) {
          // Update last updated timestamp from cache
          try {
            const cacheData = localStorage.getItem(getCacheKey(provider))
            if (cacheData) {
              const cache: ModelCache = JSON.parse(cacheData)
              lastUpdated.value[provider] = cache.lastFetched
            }
          } catch (error) {
            // Ignore errors
          }
        }
      })
    }
  })

  return {
    // State
    loading: readonly(loading),
    lastUpdated: readonly(lastUpdated),

    // Methods
    getModels,
    refreshCache,
    refreshAllCaches,
    clearCache,
    clearAllCaches,
    getCacheStatus,
    isCacheValid,
  }
}
