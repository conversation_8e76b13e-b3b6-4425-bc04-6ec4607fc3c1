import type { Unsubscribe } from 'firebase/firestore'
import type { Integration, IntegrationFormData, UseIntegrationsOptions } from '../types/integration'
import {
  addDoc,
  collection,
  doc,
  onSnapshot,
  orderBy,
  query,
  serverTimestamp,
  Timestamp,

  updateDoc,
  where,
} from 'firebase/firestore'
import { maskApi<PERSON>ey } from '../utils/encryption'

export function useIntegrations(options?: UseIntegrationsOptions) {
  const { $clientFirestore } = useNuxtApp()
  const { currentUser, currentWorkspace, currentProfile } = useAuth()

  // State
  const integrations = ref<Integration[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Unsubscribe function for cleanup
  let unsubscribe: Unsubscribe | null = null

  // Get effective options with defaults
  const getOptions = (): Required<UseIntegrationsOptions> => {
    return {
      userId: options?.userId || currentUser.value?.id || '',
      workspaceId: options?.workspaceId || currentWorkspace.value?.id || '',
      profileId: options?.profileId || currentProfile.value?.id || '',
      includeInherited: options?.includeInherited ?? true,
    }
  }

  // Build query based on options
  const buildQuery = () => {
    const opts = getOptions()
    const integrationsRef = collection($clientFirestore, 'integrations')
    const constraints = []

    if (opts.profileId && opts.includeInherited) {
      // For profile view, include both user-level and profile-specific integrations
      constraints.push(
        where('userId', '==', opts.userId),
        where('deletedAt', '==', null),
      )
    }
    else if (opts.userId) {
      // For user view, only show user's integrations
      constraints.push(
        where('userId', '==', opts.userId),
        where('deletedAt', '==', null),
      )
    }

    constraints.push(orderBy('createdAt', 'desc'))

    return query(integrationsRef, ...constraints)
  }

  // Filter integrations based on current context
  const filterIntegrations = (allIntegrations: Integration[]) => {
    const opts = getOptions()

    if (opts.profileId) {
      // In profile context, show profile-specific and inherited user integrations
      return allIntegrations.filter((integration) => {
        // Profile-specific integration
        if (integration.profileId === opts.profileId) {
          return true
        }

        // User-level integration available to profiles
        if (!integration.profileId && integration.availableToProfiles) {
          return true
        }

        return false
      })
    }
    else {
      // In user context, show only user-level integrations
      return allIntegrations.filter(integration => !integration.profileId)
    }
  }

  // Subscribe to integrations
  const subscribe = () => {
    loading.value = true
    error.value = null

    try {
      const q = buildQuery()

      unsubscribe = onSnapshot(
        q,
        (snapshot) => {
          const allIntegrations = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
          })) as Integration[]

          // Filter based on context
          integrations.value = filterIntegrations(allIntegrations)
          loading.value = false
        },
        (err) => {
          console.error('Error fetching integrations:', err)
          error.value = err.message
          loading.value = false
        },
      )
    }
    catch (err) {
      console.error('Error setting up integrations subscription:', err)
      error.value = (err as Error).message
      loading.value = false
    }
  }

  // Create a new integration
  const createIntegration = async (data: IntegrationFormData) => {
    const opts = getOptions()

    if (!opts.userId) {
      throw new Error('User not authenticated')
    }

    try {
      loading.value = true
      error.value = null

      // Validate API key with provider
      const validation = await $fetch('/api/integrations/validate', {
        method: 'POST',
        body: {
          apiKey: data.apiKey,
          provider: data.provider,
        },
      })

      if (!validation.isValid) {
        throw new Error(validation.error || 'Invalid API key')
      }

      // Encrypt API key
      const encryption = await $fetch('/api/integrations/encrypt', {
        method: 'POST',
        body: {
          apiKey: data.apiKey,
          provider: data.provider,
        },
      })

      // Prepare integration document
      const integrationData: Omit<Integration, 'id'> = {
        userId: opts.userId,
        workspaceId: opts.workspaceId || undefined,
        profileId: opts.profileId || undefined,
        provider: data.provider,
        name: data.name,
        description: data.description,
        credentials: {
          apiKey: encryption.encryptedKey,
          encryptedAt: Timestamp.now(),
        },
        settings: {
          defaultModel: data.settings?.defaultModel,
          baseUrl: data.settings?.baseUrl,
          maxTokens: data.settings?.maxTokens,
          temperature: data.settings?.temperature,
        },
        isActive: true,
        isDefault: false,
        availableToProfiles: data.availableToProfiles ?? true,
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
        deletedAt: null,
      }

      // Create in Firestore
      const integrationsRef = collection($clientFirestore, 'integrations')
      const docRef = await addDoc(integrationsRef, integrationData)

      // Return the created integration with masked API key
      return {
        id: docRef.id,
        ...integrationData,
        credentials: {
          ...integrationData.credentials,
          apiKey: maskApiKey(data.apiKey),
        },
      }
    }
    catch (err) {
      error.value = (err as Error).message
      throw err
    }
    finally {
      loading.value = false
    }
  }

  // Update an existing integration
  const updateIntegration = async (id: string, updates: Partial<Integration>) => {
    try {
      loading.value = true
      error.value = null

      const docRef = doc($clientFirestore, 'integrations', id)

      // Remove fields that shouldn't be updated
      const { id: _, userId, createdAt, ...safeUpdates } = updates

      await updateDoc(docRef, {
        ...safeUpdates,
        updatedAt: serverTimestamp(),
      })
    }
    catch (err) {
      error.value = (err as Error).message
      throw err
    }
    finally {
      loading.value = false
    }
  }

  // Toggle integration active status
  const toggleIntegration = async (id: string, isActive: boolean) => {
    await updateIntegration(id, { isActive })
  }

  // Set as default for provider
  const setDefaultIntegration = async (id: string, provider: string) => {
    try {
      loading.value = true

      // First, unset any existing default for this provider
      const currentDefaults = integrations.value.filter(
        i => i.provider === provider && i.isDefault && i.id !== id,
      )

      for (const integration of currentDefaults) {
        await updateIntegration(integration.id, { isDefault: false })
      }

      // Then set the new default
      await updateIntegration(id, { isDefault: true })
    }
    finally {
      loading.value = false
    }
  }

  // Soft delete an integration
  const deleteIntegration = async (id: string) => {
    await updateIntegration(id, {
      deletedAt: serverTimestamp() as Timestamp,
      isActive: false,
    })
  }

  // Get integrations by provider
  const getIntegrationsByProvider = (provider: string) => {
    return computed(() => integrations.value.filter(i => i.provider === provider))
  }

  // Get default integration for a provider
  const getDefaultIntegration = (provider: string) => {
    return computed(() =>
      integrations.value.find(i => i.provider === provider && i.isDefault && i.isActive)
      || integrations.value.find(i => i.provider === provider && i.isActive),
    )
  }

  // Lifecycle
  onMounted(() => {
    if (currentUser.value) {
      subscribe()
    }
  })

  onUnmounted(() => {
    if (unsubscribe) {
      unsubscribe()
    }
  })

  // Watch for user changes
  watch(currentUser, (newUser) => {
    if (newUser) {
      subscribe()
    }
    else {
      integrations.value = []
      if (unsubscribe) {
        unsubscribe()
      }
    }
  })

  return {
    integrations: readonly(integrations),
    loading: readonly(loading),
    error: readonly(error),
    createIntegration,
    updateIntegration,
    toggleIntegration,
    setDefaultIntegration,
    deleteIntegration,
    getIntegrationsByProvider,
    getDefaultIntegration,
  }
}
