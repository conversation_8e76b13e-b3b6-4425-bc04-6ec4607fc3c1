import type { Integration, LLMProvider } from '../types/integration'

/**
 * Enhanced integration inheritance status for multi-level management
 */
export interface IntegrationInheritanceStatus {
  /** Whether this integration is profile-specific */
  isProfileSpecific: boolean
  /** Whether this integration is inherited from user level */
  isInherited: boolean
  /** Whether this integration overrides a user-level integration */
  isOverridden: boolean
  /** Whether this integration is effectively active for the profile */
  isEffective: boolean
  /** Source of the integration (profile or user) */
  source: 'profile' | 'user'
  /** User-level integration that this inherits from (if any) */
  parentIntegration?: Integration
  /** Conflict information if there are issues */
  conflict?: {
    type: 'duplicate_provider' | 'conflicting_settings' | 'deprecated_parent'
    message: string
    severity: 'warning' | 'error'
  }
}

/**
 * Resolved integration with inheritance information
 */
export interface ResolvedIntegration {
  integration: Integration
  status: IntegrationInheritanceStatus
  effectiveSettings: Integration['settings']
}

export function useIntegrationInheritance() {
  const { currentUser, currentProfile, currentWorkspace } = useAuth()

  // Enhanced state management
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Get effective integration for a provider considering inheritance
  const getEffectiveIntegration = (
    integrations: Integration[],
    provider: string,
  ): Integration | null => {
    if (!currentProfile.value) {
      // User context - just find active integration for provider
      return integrations.find(i =>
        i.provider === provider
        && i.isActive
        && !i.profileId,
      ) || null
    }

    // Profile context - check profile-specific first, then inherited
    const profileIntegration = integrations.find(i =>
      i.provider === provider
      && i.isActive
      && i.profileId === currentProfile.value?.id,
    )

    if (profileIntegration) {
      return profileIntegration
    }

    // Fall back to user-level integration if available to profiles
    const userIntegration = integrations.find(i =>
      i.provider === provider
      && i.isActive
      && !i.profileId
      && i.availableToProfiles,
    )

    return userIntegration || null
  }

  // Check if an integration is inherited (user-level shown in profile context)
  const isInherited = (integration: Integration): boolean => {
    if (!currentProfile.value)
      return false
    return !integration.profileId && integration.availableToProfiles
  }

  // Check if an integration is overridden (profile has its own)
  const isOverridden = (
    integrations: Integration[],
    userIntegration: Integration,
  ): boolean => {
    if (!currentProfile.value)
      return false

    return integrations.some(i =>
      i.provider === userIntegration.provider
      && i.profileId === currentProfile.value?.id
      && i.isActive,
    )
  }

  // Enhanced inheritance status with conflict detection
  const getInheritanceStatus = (
    integration: Integration,
    allIntegrations: Integration[],
  ): IntegrationInheritanceStatus => {
    const isProfileSpecific = !!integration.profileId
    const inherited = isInherited(integration)
    const overridden = !isProfileSpecific && isOverridden(allIntegrations, integration)

    // Find parent integration if this is a profile override
    let parentIntegration: Integration | undefined
    if (isProfileSpecific) {
      parentIntegration = allIntegrations.find(i =>
        i.provider === integration.provider &&
        !i.profileId &&
        i.availableToProfiles
      )
    }

    // Detect conflicts
    let conflict: IntegrationInheritanceStatus['conflict']

    // Check for duplicate providers in same context
    const sameProviderIntegrations = allIntegrations.filter(i =>
      i.provider === integration.provider &&
      i.isActive &&
      i.id !== integration.id
    )

    if (sameProviderIntegrations.length > 0) {
      conflict = {
        type: 'duplicate_provider',
        message: `Multiple active integrations for ${integration.provider}`,
        severity: 'warning',
      }
    }

    // Check for deprecated parent
    if (parentIntegration && !parentIntegration.isActive) {
      conflict = {
        type: 'deprecated_parent',
        message: 'Parent integration is no longer active',
        severity: 'error',
      }
    }

    return {
      isProfileSpecific,
      isInherited: inherited,
      isOverridden: overridden,
      isEffective: !overridden && integration.isActive,
      source: isProfileSpecific ? 'profile' : 'user',
      parentIntegration,
      conflict,
    }
  }

  // Group integrations by provider with inheritance info
  const groupIntegrationsByProvider = (integrations: Integration[]) => {
    const grouped = new Map<string, {
      provider: string
      userIntegration?: Integration
      profileIntegration?: Integration
      effective?: Integration
      hasOverride: boolean
    }>()

    integrations.forEach((integration) => {
      const provider = integration.provider
      let group = grouped.get(provider)

      if (!group) {
        group = {
          provider,
          hasOverride: false,
        }
        grouped.set(provider, group)
      }

      if (integration.profileId === currentProfile.value?.id) {
        group.profileIntegration = integration
        group.hasOverride = true
      }
      else if (!integration.profileId) {
        group.userIntegration = integration
      }
    })

    // Determine effective integration for each provider
    grouped.forEach((group) => {
      group.effective = getEffectiveIntegration(integrations, group.provider)
    })

    return Array.from(grouped.values())
  }

  // Get summary of inheritance for current context
  const getInheritanceSummary = (integrations: Integration[]) => {
    const summary = {
      totalIntegrations: integrations.length,
      userIntegrations: 0,
      profileIntegrations: 0,
      inheritedIntegrations: 0,
      overriddenIntegrations: 0,
    }

    integrations.forEach((integration) => {
      if (integration.profileId) {
        summary.profileIntegrations++
      }
      else {
        summary.userIntegrations++
        if (currentProfile.value && integration.availableToProfiles) {
          summary.inheritedIntegrations++
          if (isOverridden(integrations, integration)) {
            summary.overriddenIntegrations++
          }
        }
      }
    })

    return summary
  }

  // Helper to determine if we should show inheritance UI
  const shouldShowInheritanceUI = computed(() => {
    return !!currentProfile.value
  })

  // Helper to get inheritance label for UI
  const getInheritanceLabel = (integration: Integration, allIntegrations: Integration[]): string => {
    if (!currentProfile.value)
      return ''

    const status = getInheritanceStatus(integration, allIntegrations)

    if (status.isProfileSpecific) {
      return 'Profile-specific'
    }
    else if (status.isInherited && status.isOverridden) {
      return 'Inherited (overridden)'
    }
    else if (status.isInherited) {
      return 'Inherited from user'
    }

    return 'User-level'
  }

  // Helper to get inheritance icon
  const getInheritanceIcon = (integration: Integration, allIntegrations: Integration[]): string => {
    const status = getInheritanceStatus(integration, allIntegrations)

    if (status.isProfileSpecific) {
      return 'ph:user-circle'
    }
    else if (status.isInherited && status.isOverridden) {
      return 'ph:link-break'
    }
    else if (status.isInherited) {
      return 'ph:link'
    }

    return 'ph:users'
  }

  // Advanced methods for Phase 3

  // Toggle profile availability for user-level integrations
  const toggleProfileAvailability = async (integrationId: string, available: boolean) => {
    try {
      loading.value = true
      error.value = null

      await $fetch(`/api/integrations/${integrationId}`, {
        method: 'PATCH',
        body: { availableToProfiles: available },
      })

      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update profile availability'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Create a profile override for an inherited integration
  const createProfileOverride = async (
    userIntegrationId: string,
    profileId: string,
    overrideData: Partial<Integration>
  ) => {
    try {
      loading.value = true
      error.value = null

      const response = await $fetch('/api/integrations', {
        method: 'POST',
        body: {
          ...overrideData,
          profileId,
          parentIntegrationId: userIntegrationId,
        },
      })

      return response.integration
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create profile override'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Remove a profile override and revert to inherited integration
  const removeProfileOverride = async (profileIntegrationId: string) => {
    try {
      loading.value = true
      error.value = null

      await $fetch(`/api/integrations/${profileIntegrationId}`, {
        method: 'DELETE',
      })

      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to remove profile override'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Get all effective integrations for current context with full inheritance info
  const getEffectiveIntegrationsWithStatus = (integrations: Integration[]): ResolvedIntegration[] => {
    const resolved: ResolvedIntegration[] = []
    const grouped = groupIntegrationsByProvider(integrations)

    grouped.forEach(group => {
      if (group.effective) {
        const status = getInheritanceStatus(group.effective, integrations)
        resolved.push({
          integration: group.effective,
          status,
          effectiveSettings: group.effective.settings,
        })
      }
    })

    return resolved
  }

  // Detect and resolve integration conflicts
  const detectConflicts = (integrations: Integration[]) => {
    const conflicts: Array<{
      integration: Integration
      conflict: IntegrationInheritanceStatus['conflict']
    }> = []

    integrations.forEach(integration => {
      const status = getInheritanceStatus(integration, integrations)
      if (status.conflict) {
        conflicts.push({
          integration,
          conflict: status.conflict,
        })
      }
    })

    return conflicts
  }

  // Auto-resolve conflicts where possible
  const resolveConflicts = async (integrations: Integration[]) => {
    const conflicts = detectConflicts(integrations)
    const resolutions: Array<{ action: string; integrationId: string }> = []

    for (const { integration, conflict } of conflicts) {
      if (conflict.type === 'duplicate_provider' && conflict.severity === 'warning') {
        // For duplicate providers, prefer profile-specific over inherited
        const sameProviderIntegrations = integrations.filter(i =>
          i.provider === integration.provider &&
          i.isActive &&
          i.id !== integration.id
        )

        // If this is a user integration and there's a profile override, deactivate user integration
        if (!integration.profileId && sameProviderIntegrations.some(i => i.profileId)) {
          try {
            await $fetch(`/api/integrations/${integration.id}`, {
              method: 'PATCH',
              body: { isActive: false },
            })

            resolutions.push({
              action: 'deactivated_user_integration',
              integrationId: integration.id,
            })
          } catch (err) {
            console.error('Failed to resolve conflict:', err)
          }
        }
      }
    }

    return resolutions
  }

  // Get inheritance summary with enhanced metrics
  const getEnhancedInheritanceSummary = (integrations: Integration[]) => {
    const summary = getInheritanceSummary(integrations)
    const conflicts = detectConflicts(integrations)
    const resolved = getEffectiveIntegrationsWithStatus(integrations)

    return {
      ...summary,
      conflictCount: conflicts.length,
      effectiveIntegrations: resolved.length,
      hasConflicts: conflicts.length > 0,
      conflicts,
      resolved,
    }
  }

  return {
    // Enhanced state
    loading: readonly(loading),
    error: readonly(error),

    // Original methods
    getEffectiveIntegration,
    isInherited,
    isOverridden,
    getInheritanceStatus,
    groupIntegrationsByProvider,
    getInheritanceSummary,
    shouldShowInheritanceUI,
    getInheritanceLabel,
    getInheritanceIcon,

    // New Phase 3 methods
    toggleProfileAvailability,
    createProfileOverride,
    removeProfileOverride,
    getEffectiveIntegrationsWithStatus,
    detectConflicts,
    resolveConflicts,
    getEnhancedInheritanceSummary,
  }
}
