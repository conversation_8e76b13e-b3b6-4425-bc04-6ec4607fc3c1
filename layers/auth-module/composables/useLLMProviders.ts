import type { LLMModel, <PERSON><PERSON><PERSON>iderConfig, LLMProvider, ModelFetchOptions } from '../types/integration'
import {
  getAllLLMProviders,
  getLLMProvider,
  getProviderModels,
  supportsModelFetching,
  mergeModels
} from '../config/llm-providers'
import { useModelCache } from './useModelCache'

export function useLLMProviders() {
  const modelCache = useModelCache()

  // State for dynamic models (fetched from API)
  const dynamicModels = ref<Record<string, LLMModel[]>>({})
  const modelsLoading = ref<Record<LLMProvider, boolean>>({
    openai: false,
    anthropic: false,
    google: false,
    xai: false,
  })
  const modelsError = ref<Record<LLMProvider, string | null>>({
    openai: null,
    anthropic: null,
    google: null,
    xai: null,
  })

  // Get all available providers
  const providers = computed(() => getAllLLMProviders())

  // Get a specific provider
  const getProvider = (providerId: string): LLMProviderConfig | undefined => {
    return getLLMProvider(providerId)
  }

  // Get models for a provider (with dynamic fallback)
  const getModels = (providerId: string): LLMModel[] => {
    // Check if we have dynamic models
    if (dynamicModels.value[providerId]) {
      return dynamicModels.value[providerId]
    }

    // Fall back to static models
    return getProviderModels(providerId)
  }

  // Get models with API key for dynamic fetching
  const getModelsWithApiKey = async (
    provider: LLMProvider,
    apiKey?: string,
    options: ModelFetchOptions = {}
  ): Promise<LLMModel[]> => {
    const providerId = provider as string
    modelsLoading.value[provider] = true
    modelsError.value[provider] = null

    try {
      // Get static models as fallback
      const staticModels = getProviderModels(providerId)

      // If no API key provided, return static models
      if (!apiKey) {
        dynamicModels.value[providerId] = staticModels
        return staticModels
      }

      // Check if provider supports dynamic fetching
      if (!supportsModelFetching(providerId)) {
        dynamicModels.value[providerId] = staticModels
        return staticModels
      }

      // Fetch models using cache
      const result = await modelCache.getModels(provider, apiKey, options)

      if (result.success) {
        // Merge dynamic models with static fallbacks
        const mergedModels = mergeModels(staticModels, result.models)
        dynamicModels.value[providerId] = mergedModels
        return mergedModels
      } else {
        // On error, use static models and store error
        modelsError.value[provider] = result.error || 'Failed to fetch models'
        dynamicModels.value[providerId] = staticModels
        return staticModels
      }
    } catch (error) {
      // On exception, use static models and store error
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      modelsError.value[provider] = errorMessage
      const staticModels = getProviderModels(providerId)
      dynamicModels.value[providerId] = staticModels
      return staticModels
    } finally {
      modelsLoading.value[provider] = false
    }
  }

  // Fetch latest models from API (legacy method for backward compatibility)
  const fetchLatestModels = async (providerId?: string) => {
    try {
      const params = providerId ? `?provider=${providerId}` : ''
      const response = await $fetch(`/api/integrations/models${params}`)

      if (response.success) {
        if (providerId && response.models) {
          // Update specific provider
          dynamicModels.value[providerId] = response.models
        }
        else if (response.providers) {
          // Update all providers
          response.providers.forEach((provider: any) => {
            dynamicModels.value[provider.id] = provider.models
          })
        }
      }
    }
    catch (error) {
      console.error('Failed to fetch models:', error)
      const provider = providerId as LLMProvider
      if (provider && ['openai', 'anthropic', 'google', 'xai'].includes(provider)) {
        modelsError.value[provider] = (error as Error).message
      }
    }
  }

  // Refresh models for a provider
  const refreshModels = async (
    provider: LLMProvider,
    apiKey: string,
    options: ModelFetchOptions = {}
  ): Promise<LLMModel[]> => {
    return await getModelsWithApiKey(provider, apiKey, { ...options, forceRefresh: true })
  }

  // Get provider icon component props
  const getProviderIcon = (providerId: string) => {
    const provider = getProvider(providerId)
    return provider?.icon || 'ph:robot'
  }

  // Get provider display name
  const getProviderName = (providerId: string) => {
    const provider = getProvider(providerId)
    return provider?.name || providerId
  }

  // Get model by ID
  const getModel = (providerId: string, modelId: string): LLMModel | undefined => {
    const models = getModels(providerId)
    return models.find(m => m.id === modelId)
  }

  // Get default model for a provider
  const getDefaultModel = (providerId: string): LLMModel | undefined => {
    const models = getModels(providerId)
    return models[0] // First model is default
  }

  // Format model context window for display
  const formatContextWindow = (tokens: number): string => {
    if (tokens >= 1000000) {
      return `${(tokens / 1000000).toFixed(1)}M`
    }
    else if (tokens >= 1000) {
      return `${(tokens / 1000).toFixed(0)}K`
    }
    return tokens.toString()
  }

  // Format model pricing for display
  const formatPricing = (model: LLMModel): string => {
    if (!model.pricing)
      return 'Pricing not available'

    const { input, output, currency } = model.pricing

    if (input === 0 && output === 0) {
      return 'Free'
    }

    return `$${input}/$${output} per 1M tokens`
  }

  // Check if a model supports a specific capability
  const modelSupports = (model: LLMModel, capability: string): boolean => {
    return model.capabilities.includes(capability)
  }

  // Group models by capability
  const getModelsByCapability = (providerId: string, capability: string): LLMModel[] => {
    const models = getModels(providerId)
    return models.filter(m => modelSupports(m, capability))
  }

  // Sort models by various criteria
  const sortModels = (models: LLMModel[], criteria: 'name' | 'context' | 'price' = 'name'): LLMModel[] => {
    const sorted = [...models]

    switch (criteria) {
      case 'name':
        return sorted.sort((a, b) => a.displayName.localeCompare(b.displayName))

      case 'context':
        return sorted.sort((a, b) => b.contextWindow - a.contextWindow)

      case 'price':
        return sorted.sort((a, b) => {
          const priceA = a.pricing?.input || Infinity
          const priceB = b.pricing?.input || Infinity
          return priceA - priceB
        })

      default:
        return sorted
    }
  }

  // Filter out deprecated models
  const getActiveModels = (providerId: string): LLMModel[] => {
    const models = getModels(providerId)
    return models.filter(m => !m.deprecated)
  }

  // Check if provider supports dynamic model fetching
  const providerSupportsModelFetching = (provider: LLMProvider): boolean => {
    return supportsModelFetching(provider as string)
  }

  // Get model by ID from a provider
  const getModelById = (provider: LLMProvider, modelId: string): LLMModel | undefined => {
    const models = getModels(provider as string)
    return models.find(model => model.id === modelId)
  }

  // Load dynamic models on mount
  onMounted(() => {
    // Initialize static models for all providers
    const providerList: LLMProvider[] = ['openai', 'anthropic', 'google', 'xai']
    providerList.forEach(provider => {
      if (!dynamicModels.value[provider as string]) {
        dynamicModels.value[provider as string] = getProviderModels(provider as string)
      }
    })

    // Fetch latest models in background (don't block UI)
    fetchLatestModels().catch(console.error)
  })

  return {
    providers,
    dynamicModels: readonly(dynamicModels),
    modelsLoading: readonly(modelsLoading),
    modelsError: readonly(modelsError),
    getProvider,
    getModels,
    getModelsWithApiKey,
    refreshModels,
    getModel,
    getModelById,
    getDefaultModel,
    getProviderIcon,
    getProviderName,
    providerSupportsModelFetching,
    fetchLatestModels,
    formatContextWindow,
    formatPricing,
    modelSupports,
    getModelsByCapability,
    sortModels,
    getActiveModels,

    // Cache methods (re-exported from modelCache)
    cacheLoading: modelCache.loading,
    cacheLastUpdated: modelCache.lastUpdated,
    getCacheStatus: modelCache.getCacheStatus,
    clearCache: modelCache.clearCache,
    clearAllCaches: modelCache.clearAllCaches,
  }
}
