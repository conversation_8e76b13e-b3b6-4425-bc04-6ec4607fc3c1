import type { Unsubscribe } from 'firebase/firestore'
import type { Profile, UpdateProfileData } from '../types/profile'
import {
  doc,
  getDoc,
  onSnapshot,
  serverTimestamp,
  updateDoc,
} from 'firebase/firestore'
import { useAuth } from './auth'
import { useFirebase } from './firebase'

interface ProfileState {
  profile: Profile | null
  loading: boolean
  error: Error | null
}

interface ProfileListeners {
  [key: string]: Unsubscribe
}

export function useProfile() {
  const toaster = useNuiToasts()
  const { firestore } = useFirebase()
  const { user, currentWorkspace } = useAuth()

  // Helper functions for toasts
  const showError = (message: string) => {
    toaster.add({
      title: 'Error',
      message,
      color: 'danger',
      icon: 'heroicons:x-circle',
      closable: true,
    })
  }

  const showSuccess = (message: string) => {
    toaster.add({
      title: 'Success',
      message,
      color: 'success',
      icon: 'heroicons:check-circle',
      closable: true,
    })
  }

  // Create cookies for persistent state
  const profileCookie = useCookie<Profile | null>('profile:current', {
    default: () => null,
    httpOnly: false, // Allow client-side access
    secure: true,
    sameSite: 'lax',
  })

  // Create reactive state with initial values from cookies
  const state = useState<ProfileState>('profile', () => ({
    profile: profileCookie.value || null,
    loading: false,
    error: null,
  }))

  // Store active listeners for cleanup
  const listeners = useState<ProfileListeners>('profile:listeners', () => ({}))

  // Helper to generate profile document ID
  const getProfileDocId = (userId: string, workspaceId: string): string => {
    return `${userId}_${workspaceId}`
  }

  // Get a profile by userId and workspaceId
  const getProfile = async (userId: string, workspaceId: string): Promise<Profile | null> => {
    try {
      state.value.loading = true
      state.value.error = null

      const profileDocId = getProfileDocId(userId, workspaceId)
      const profileRef = doc(firestore, 'profiles', profileDocId)
      const profileSnap = await getDoc(profileRef)

      if (profileSnap.exists()) {
        const profileData = profileSnap.data() as Profile
        const profile: Profile = {
          ...profileData,
          id: profileSnap.id,
        }

        // Update state if this is the current user's profile
        if (userId === user.value?.id && workspaceId === currentWorkspace.value?.id) {
          state.value.profile = profile
          profileCookie.value = profile
        }

        return profile
      }

      return null
    }
    catch (error: any) {
      console.error('[useProfile] Failed to get profile:', error)
      state.value.error = error
      showError('Failed to load profile')
      throw error
    }
    finally {
      state.value.loading = false
    }
  }

  // Get the current user's profile for the current workspace
  const getCurrentProfile = async (): Promise<Profile | null> => {
    if (!user.value?.id || !currentWorkspace.value?.id) {
      console.warn('[useProfile] No user or workspace set')
      return null
    }

    return getProfile(user.value.id, currentWorkspace.value.id)
  }

  // Update a profile
  const updateProfile = async (
    userId: string,
    workspaceId: string,
    data: UpdateProfileData,
  ): Promise<void> => {
    try {
      state.value.loading = true
      state.value.error = null

      const profileDocId = getProfileDocId(userId, workspaceId)
      const profileRef = doc(firestore, 'profiles', profileDocId)

      console.log('[useProfile] Updating profile:', {
        profileDocId,
        userId,
        workspaceId,
        data,
      })

      // First check if the profile exists
      const profileSnap = await getDoc(profileRef)
      if (!profileSnap.exists()) {
        throw new Error(`Profile not found for user ${userId} in workspace ${workspaceId}`)
      }

      // Get existing profile data to ensure we maintain required fields
      const existingData = profileSnap.data()
      console.log('[useProfile] Existing profile data:', existingData)

      // Add updatedAt timestamp and filter out undefined values
      const updateData: any = {}
      Object.keys(data).forEach((key) => {
        if (data[key as keyof UpdateProfileData] !== undefined) {
          updateData[key] = data[key as keyof UpdateProfileData]
        }
      })
      updateData.updatedAt = serverTimestamp()

      console.log('[useProfile] Update data to be sent:', updateData)

      // Optimistic update for current user
      if (userId === user.value?.id && workspaceId === currentWorkspace.value?.id && state.value.profile) {
        state.value.profile = {
          ...state.value.profile,
          ...data,
          updatedAt: new Date(),
        }
        profileCookie.value = state.value.profile
      }

      await updateDoc(profileRef, updateData)
      showSuccess('Profile updated successfully')
    }
    catch (error: any) {
      console.error('[useProfile] Failed to update profile:', error)
      console.error('[useProfile] Error details:', {
        code: error.code,
        message: error.message,
        serverResponse: error.serverResponse,
      })
      state.value.error = error

      // Revert optimistic update on error
      if (userId === user.value?.id && workspaceId === currentWorkspace.value?.id) {
        await getCurrentProfile()
      }

      showError(`Failed to update profile: ${error.message || 'Unknown error'}`)
      throw error
    }
    finally {
      state.value.loading = false
    }
  }

  // Update the current user's profile
  const updateCurrentProfile = async (data: UpdateProfileData): Promise<void> => {
    if (!user.value?.id || !currentWorkspace.value?.id) {
      throw new Error('No user or workspace set')
    }

    return updateProfile(user.value.id, currentWorkspace.value.id, data)
  }

  // Watch a profile for real-time updates
  const watchProfile = (userId: string, workspaceId: string, callback?: (profile: Profile | null) => void) => {
    const profileDocId = getProfileDocId(userId, workspaceId)

    // Unsubscribe from existing listener if any
    if (listeners.value[profileDocId]) {
      listeners.value[profileDocId]()
    }

    const profileRef = doc(firestore, 'profiles', profileDocId)

    const unsubscribe = onSnapshot(
      profileRef,
      (snapshot) => {
        if (snapshot.exists()) {
          const profileData = snapshot.data() as Profile
          const profile: Profile = {
            ...profileData,
            id: snapshot.id,
          }

          // Update state if this is the current user's profile
          if (userId === user.value?.id && workspaceId === currentWorkspace.value?.id) {
            state.value.profile = profile
            profileCookie.value = profile
          }

          // Call callback if provided
          if (callback) {
            callback(profile)
          }
        }
        else {
          // Profile doesn't exist
          if (userId === user.value?.id && workspaceId === currentWorkspace.value?.id) {
            state.value.profile = null
            profileCookie.value = null
          }

          if (callback) {
            callback(null)
          }
        }

        state.value.error = null
      },
      (error) => {
        console.error('[useProfile] Profile watch error:', error)
        state.value.error = error
        showError('Failed to watch profile changes')
      },
    )

    // Store the unsubscribe function
    listeners.value[profileDocId] = unsubscribe

    return unsubscribe
  }

  // Watch the current user's profile
  const watchCurrentProfile = (callback?: (profile: Profile | null) => void) => {
    if (!user.value?.id || !currentWorkspace.value?.id) {
      console.warn('[useProfile] No user or workspace set')
      return () => {} // Return empty cleanup function
    }

    return watchProfile(user.value.id, currentWorkspace.value.id, callback)
  }

  // Unwatch a specific profile
  const unwatchProfile = (userId: string, workspaceId: string) => {
    const profileDocId = getProfileDocId(userId, workspaceId)

    if (listeners.value[profileDocId]) {
      listeners.value[profileDocId]()
      delete listeners.value[profileDocId]
    }
  }

  // Unwatch all profiles
  const unwatchAllProfiles = () => {
    Object.values(listeners.value).forEach(unsubscribe => unsubscribe())
    listeners.value = {}
  }

  // Check if a profile exists
  const profileExists = async (userId: string, workspaceId: string): Promise<boolean> => {
    try {
      const profileDocId = getProfileDocId(userId, workspaceId)
      const profileRef = doc(firestore, 'profiles', profileDocId)
      const profileSnap = await getDoc(profileRef)
      return profileSnap.exists()
    }
    catch (error) {
      console.error('[useProfile] Failed to check profile existence:', error)
      return false
    }
  }

  // Clear cached profile
  const clearProfileCache = () => {
    state.value.profile = null
    profileCookie.value = null
  }

  // Refresh current profile from database
  const refreshCurrentProfile = async () => {
    if (!user.value?.id || !currentWorkspace.value?.id) {
      return null
    }

    return getCurrentProfile()
  }

  // Cleanup function to be called on component unmount
  const cleanup = () => {
    unwatchAllProfiles()
  }

  // Auto cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    profile: computed(() => state.value.profile),
    currentProfile: computed(() => state.value.profile), // Alias for convenience
    loading: computed(() => state.value.loading),
    error: computed(() => state.value.error),

    // Core methods
    getProfile,
    getCurrentProfile,
    updateProfile,
    updateCurrentProfile,
    watchProfile,
    watchCurrentProfile,
    unwatchProfile,
    unwatchAllProfiles,

    // Utility methods
    profileExists,
    clearProfileCache,
    refreshCurrentProfile,
    cleanup,

    // Helpers
    getProfileDocId,
  }
}

// Export a composable for current profile that integrates with auth
export function useCurrentProfile() {
  const { profile, loading, error, updateCurrentProfile, watchCurrentProfile, refreshCurrentProfile } = useProfile()
  const { isAuthenticated, currentWorkspace } = useAuth()

  // Auto-watch current profile when workspace changes
  watch([isAuthenticated, currentWorkspace], ([authed, workspace]) => {
    if (authed && workspace) {
      watchCurrentProfile()
    }
  }, { immediate: true })

  return {
    currentProfile: profile,
    profileLoading: loading,
    profileError: error,
    updateProfile: updateCurrentProfile,
    refreshProfile: refreshCurrentProfile,
  }
}
