import { describe, it, expect, beforeEach, vi } from 'vitest'
import { ModelFetcher } from '../services/modelFetcher'
import type { LLMProvider } from '../types/integration'

// Mock fetch globally
global.fetch = vi.fn()

describe('ModelFetcher', () => {
  let modelFetcher: ModelFetcher
  
  beforeEach(() => {
    modelFetcher = new ModelFetcher()
    vi.clearAllMocks()
  })

  describe('fetchModels', () => {
    it('should fetch OpenAI models successfully', async () => {
      const mockResponse = {
        data: [
          {
            id: 'gpt-4-turbo',
            object: 'model',
            created: **********,
            owned_by: 'openai',
          },
          {
            id: 'gpt-3.5-turbo',
            object: 'model',
            created: **********,
            owned_by: 'openai',
          },
        ],
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const result = await modelFetcher.fetchModels('openai', 'sk-test123')

      expect(result.success).toBe(true)
      expect(result.models).toHaveLength(2)
      expect(result.models[0].id).toBe('gpt-4-turbo')
      expect(result.models[0].displayName).toContain('GPT')
      expect(result.fromCache).toBe(false)
    })

    it('should handle API errors gracefully', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
      })

      const result = await modelFetcher.fetchModels('openai', 'invalid-key')

      expect(result.success).toBe(false)
      expect(result.error).toContain('OpenAI API error')
      expect(result.models).toHaveLength(0)
    })

    it('should fallback to static models on API failure', async () => {
      ;(fetch as any).mockRejectedValueOnce(new Error('Network error'))

      const result = await modelFetcher.fetchModels('openai', 'sk-test123', {
        fallbackToStatic: true,
      })

      expect(result.success).toBe(true)
      expect(result.models.length).toBeGreaterThan(0) // Should have static models
    })

    it('should handle timeout correctly', async () => {
      ;(fetch as any).mockImplementationOnce(() => 
        new Promise(resolve => setTimeout(resolve, 2000))
      )

      const result = await modelFetcher.fetchModels('openai', 'sk-test123', {
        timeout: 100, // Very short timeout
      })

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
    })
  })

  describe('provider-specific transformations', () => {
    it('should transform Anthropic models correctly', async () => {
      const mockResponse = {
        data: [
          {
            id: 'claude-3-5-sonnet-20241022',
            type: 'model',
            display_name: 'Claude 3.5 Sonnet',
            max_tokens: 200000,
            capabilities: ['chat', 'vision'],
          },
        ],
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const result = await modelFetcher.fetchModels('anthropic', 'sk-ant-test123')

      expect(result.success).toBe(true)
      expect(result.models[0].id).toBe('claude-3-5-sonnet-20241022')
      expect(result.models[0].displayName).toBe('Claude 3.5 Sonnet')
      expect(result.models[0].contextWindow).toBe(200000)
    })

    it('should transform Google models correctly', async () => {
      const mockResponse = {
        models: [
          {
            name: 'models/gemini-1.5-pro',
            displayName: 'Gemini 1.5 Pro',
            inputTokenLimit: 1000000,
            supportedGenerationMethods: ['generateContent'],
          },
        ],
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const result = await modelFetcher.fetchModels('google', 'test-api-key')

      expect(result.success).toBe(true)
      expect(result.models[0].id).toBe('gemini-1.5-pro')
      expect(result.models[0].contextWindow).toBe(1000000)
    })

    it('should transform xAI models correctly', async () => {
      const mockResponse = {
        data: [
          {
            id: 'grok-beta',
            context_length: 131072,
          },
        ],
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const result = await modelFetcher.fetchModels('xai', 'test-api-key')

      expect(result.success).toBe(true)
      expect(result.models[0].id).toBe('grok-beta')
      expect(result.models[0].contextWindow).toBe(131072)
      expect(result.models[0].capabilities).toContain('chat')
    })
  })

  describe('model transformation utilities', () => {
    it('should format display names correctly', async () => {
      const mockResponse = {
        data: [
          { id: 'gpt-4-turbo-preview' },
          { id: 'claude-3-5-sonnet-20241022' },
          { id: 'gemini-1.5-pro' },
          { id: 'grok-beta' },
        ],
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const result = await modelFetcher.fetchModels('openai', 'sk-test123')

      expect(result.models[0].displayName).toBe('GPT 4 Turbo Preview')
    })

    it('should extract release dates from model IDs', async () => {
      const mockResponse = {
        data: [
          { id: 'claude-3-5-sonnet-20241022' },
          { id: 'gpt-4-turbo-2024-04-09' },
        ],
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const result = await modelFetcher.fetchModels('openai', 'sk-test123')

      // Should extract date from model ID
      expect(result.models.some(m => m.releaseDate)).toBe(true)
    })

    it('should assign appropriate context windows', async () => {
      const mockResponse = {
        data: [
          { id: 'gpt-4-turbo' },
          { id: 'gpt-4' },
          { id: 'gpt-3.5-turbo' },
        ],
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      })

      const result = await modelFetcher.fetchModels('openai', 'sk-test123')

      const gpt4Turbo = result.models.find(m => m.id === 'gpt-4-turbo')
      const gpt4 = result.models.find(m => m.id === 'gpt-4')
      const gpt35 = result.models.find(m => m.id === 'gpt-3.5-turbo')

      expect(gpt4Turbo?.contextWindow).toBe(128000)
      expect(gpt4?.contextWindow).toBe(8192)
      expect(gpt35?.contextWindow).toBe(16384)
    })
  })

  describe('error handling and retries', () => {
    it('should retry on failure', async () => {
      ;(fetch as any)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ data: [{ id: 'gpt-4' }] }),
        })

      const result = await modelFetcher.fetchModels('openai', 'sk-test123')

      expect(fetch).toHaveBeenCalledTimes(2)
      expect(result.success).toBe(true)
    })

    it('should fail after max retries', async () => {
      ;(fetch as any)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))

      const result = await modelFetcher.fetchModels('openai', 'sk-test123')

      expect(fetch).toHaveBeenCalledTimes(2)
      expect(result.success).toBe(false)
    })
  })
})

// Test the model cache composable
describe('useModelCache', () => {
  // Note: These would be integration tests that require a browser environment
  // For now, we'll focus on the ModelFetcher unit tests above
  
  it('should be tested in a browser environment', () => {
    // Placeholder for browser-based tests
    expect(true).toBe(true)
  })
})
