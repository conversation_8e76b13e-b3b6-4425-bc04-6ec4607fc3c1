<script setup lang="ts">
import type { Integration, IntegrationFormData, LLMProvider } from '~/layers/auth-module/types/integration'

definePageMeta({
  title: 'LLM Integrations',
  layout: 'sidebar',
})

// Composables
const { currentUser, currentProfile } = useAuth()
const { integrations, loading, error, createIntegration, updateIntegration, deleteIntegration, refreshIntegrations } = useIntegrations()
const { providers, getProviderName, getProviderIcon } = useLLMProviders()
const {
  toggleProfileAvailability,
  getInheritanceStatus,
  shouldShowInheritanceUI,
  getInheritanceLabel,
  getInheritanceIcon,
  getEnhancedInheritanceSummary,
  detectConflicts,
  resolveConflicts,
  loading: inheritanceLoading,
  error: inheritanceError,
} = useIntegrationInheritance()

// State
const showAddForm = ref(false)
const showSettings = ref(false)
const selectedIntegration = ref<Integration | null>(null)
const searchQuery = ref('')
const selectedProvider = ref<LLMProvider | ''>('')
const showConflictResolution = ref(false)

// Computed properties
const connectedIntegrations = computed(() => {
  return integrations.value.filter(integration =>
    integration.isActive
    && !integration.profileId // User-level only
    && integration.userId === currentUser.value?.uid,
  )
})

const availableProviders = computed(() => {
  const connectedProviderIds = new Set(connectedIntegrations.value.map(i => i.provider))
  return providers.value.filter(provider => !connectedProviderIds.has(provider.id))
})

const filteredConnectedIntegrations = computed(() => {
  let filtered = connectedIntegrations.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(integration =>
      integration.name.toLowerCase().includes(query)
      || integration.provider.toLowerCase().includes(query)
      || integration.description?.toLowerCase().includes(query),
    )
  }

  if (selectedProvider.value) {
    filtered = filtered.filter(integration => integration.provider === selectedProvider.value)
  }

  return filtered
})

const filteredAvailableProviders = computed(() => {
  let filtered = availableProviders.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(provider =>
      provider.name.toLowerCase().includes(query)
      || provider.description.toLowerCase().includes(query),
    )
  }

  return filtered
})

const inheritanceSummary = computed(() => {
  return getEnhancedInheritanceSummary(integrations.value)
})

const conflicts = computed(() => {
  return detectConflicts(integrations.value)
})

// Methods
const handleCreateIntegration = async (data: IntegrationFormData) => {
  try {
    await createIntegration(data)
    showAddForm.value = false
    await refreshIntegrations()
  } catch (error) {
    console.error('Failed to create integration:', error)
  }
}

const handleUpdateIntegration = async (id: string, updates: Partial<Integration>) => {
  try {
    await updateIntegration(id, updates)
    await refreshIntegrations()
  } catch (error) {
    console.error('Failed to update integration:', error)
  }
}

const handleDeleteIntegration = async (id: string) => {
  try {
    await deleteIntegration(id)
    await refreshIntegrations()
  } catch (error) {
    console.error('Failed to delete integration:', error)
  }
}

const handleToggleIntegration = async (id: string, active: boolean) => {
  await handleUpdateIntegration(id, { isActive: active })
}

const handleToggleProfileAvailability = async (id: string, available: boolean) => {
  try {
    await toggleProfileAvailability(id, available)
    await refreshIntegrations()
  } catch (error) {
    console.error('Failed to toggle profile availability:', error)
  }
}

const handleEditIntegration = (integration: Integration) => {
  selectedIntegration.value = integration
  showAddForm.value = true
}

const handleViewSettings = (integration: Integration) => {
  selectedIntegration.value = integration
  showSettings.value = true
}

const handleResolveConflicts = async () => {
  try {
    const resolutions = await resolveConflicts(integrations.value)
    await refreshIntegrations()

    if (resolutions.length > 0) {
      // Show success message
      console.log('Conflicts resolved:', resolutions)
    }
  } catch (error) {
    console.error('Failed to resolve conflicts:', error)
  }
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedProvider.value = ''
}

// Initialize
onMounted(async () => {
  await refreshIntegrations()
})
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <BaseHeading as="h1" size="2xl" weight="bold" class="mb-2">
          LLM Integrations
        </BaseHeading>
        <BaseParagraph class="text-muted-600 dark:text-muted-400">
          Manage your AI model integrations and configure profile sharing
        </BaseParagraph>
      </div>

      <div class="flex items-center gap-3">
        <!-- Conflict resolution -->
        <BaseButton
          v-if="conflicts.length > 0"
          variant="soft"
          color="warning"
          @click="showConflictResolution = true"
        >
          <Icon name="ph:warning" class="size-4" />
          {{ conflicts.length }} Conflict{{ conflicts.length > 1 ? 's' : '' }}
        </BaseButton>

        <!-- Add integration -->
        <BaseButton
          color="primary"
          @click="showAddForm = true"
        >
          <Icon name="ph:plus" class="size-4" />
          Add Integration
        </BaseButton>
      </div>
    </div>

    <!-- Inheritance Summary (when profiles exist) -->
    <div v-if="shouldShowInheritanceUI" class="bg-muted-50 dark:bg-muted-900 rounded-lg p-4">
      <div class="flex items-center gap-3 mb-3">
        <Icon name="ph:users" class="size-5 text-primary-500" />
        <BaseHeading as="h3" size="md" weight="medium">
          Profile Sharing Summary
        </BaseHeading>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <span class="text-muted-600 dark:text-muted-400">Total Integrations:</span>
          <span class="ml-2 font-medium">{{ inheritanceSummary.totalIntegrations }}</span>
        </div>
        <div>
          <span class="text-muted-600 dark:text-muted-400">Available to Profiles:</span>
          <span class="ml-2 font-medium">{{ inheritanceSummary.inheritedIntegrations }}</span>
        </div>
        <div>
          <span class="text-muted-600 dark:text-muted-400">Profile Overrides:</span>
          <span class="ml-2 font-medium">{{ inheritanceSummary.overriddenIntegrations }}</span>
        </div>
        <div>
          <span class="text-muted-600 dark:text-muted-400">Conflicts:</span>
          <span class="ml-2 font-medium" :class="conflicts.length > 0 ? 'text-warning-600' : ''">
            {{ conflicts.length }}
          </span>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="flex flex-col sm:flex-row gap-4">
      <div class="flex-1">
        <BaseInput
          v-model="searchQuery"
          icon="ph:magnifying-glass"
          placeholder="Search integrations..."
          shape="curved"
        />
      </div>

      <div class="sm:w-48">
        <BaseSelect
          v-model="selectedProvider"
          icon="ph:funnel"
          shape="curved"
        >
          <option value="">
            All Providers
          </option>
          <option
            v-for="provider in providers"
            :key="provider.id"
            :value="provider.id"
          >
            {{ provider.name }}
          </option>
        </BaseSelect>
      </div>

      <BaseButton
        v-if="searchQuery || selectedProvider"
        variant="soft"
        @click="clearFilters"
      >
        <Icon name="ph:x" class="size-4" />
        Clear
      </BaseButton>
    </div>

    <!-- Connected Integrations Section -->
    <div>
      <div class="flex items-center justify-between mb-6">
        <div>
          <BaseHeading as="h2" size="lg" weight="medium" class="mb-1">
            Connected Integrations
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
            Manage your active LLM integrations and configure profile sharing
          </BaseParagraph>
        </div>

        <BaseBadge
          v-if="filteredConnectedIntegrations.length > 0"
          color="primary"
          size="sm"
        >
          {{ filteredConnectedIntegrations.length }} Active
        </BaseBadge>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3">
          <Icon name="ph:spinner" class="size-5 animate-spin text-primary-500" />
          <BaseText>Loading integrations...</BaseText>
        </div>
      </div>

      <!-- Error State -->
      <BaseAlert v-else-if="error" color="danger" class="mb-6">
        {{ error }}
      </BaseAlert>

      <!-- Connected Integrations Grid -->
      <div v-else-if="filteredConnectedIntegrations.length > 0" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TairoIntegrationCard
          v-for="integration in filteredConnectedIntegrations"
          :key="integration.id"
          :integration="integration"
          :show-inheritance="shouldShowInheritanceUI"
          :inheritance-status="getInheritanceStatus(integration, integrations)"
          :show-model-details="true"
          :show-usage-stats="true"
          :show-quick-actions="true"
          @toggle="handleToggleIntegration(integration.id, $event)"
          @edit="handleEditIntegration(integration)"
          @delete="handleDeleteIntegration(integration.id)"
          @test="() => {}"
          @duplicate="() => {}"
          @view-usage="handleViewSettings(integration)"
        >
          <!-- Profile availability toggle -->
          <template v-if="shouldShowInheritanceUI" #actions>
            <div class="flex items-center gap-2 mt-3 pt-3 border-t border-muted-200 dark:border-muted-800">
              <Icon name="ph:users" class="size-4 text-muted-400" />
              <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
                Available to profiles
              </BaseText>
              <BaseSwitchBall
                :model-value="integration.availableToProfiles"
                size="sm"
                @update:model-value="handleToggleProfileAvailability(integration.id, $event)"
              />
            </div>
          </template>
        </TairoIntegrationCard>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <Icon name="ph:robot" class="size-16 mx-auto mb-4 text-muted-400" />
        <BaseHeading as="h3" size="lg" weight="medium" class="mb-2">
          {{ searchQuery || selectedProvider ? 'No matching integrations' : 'No integrations connected' }}
        </BaseHeading>
        <BaseParagraph class="text-muted-600 dark:text-muted-400 mb-6">
          {{
            searchQuery || selectedProvider
              ? 'Try adjusting your search or filters.'
              : 'Connect your first LLM integration to get started with AI-powered features.'
          }}
        </BaseParagraph>
        <BaseButton
          v-if="!searchQuery && !selectedProvider"
          color="primary"
          @click="showAddForm = true"
        >
          <Icon name="ph:plus" class="size-4" />
          Add Your First Integration
        </BaseButton>
      </div>
    </div>

    <!-- Available Integrations Section -->
    <div>
      <div class="flex items-center justify-between mb-6">
        <div>
          <BaseHeading as="h2" size="lg" weight="medium" class="mb-1">
            Available Integrations
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
            Connect new LLM providers to expand your AI capabilities
          </BaseParagraph>
        </div>
      </div>

      <!-- Available Providers Grid -->
      <div v-if="filteredAvailableProviders.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          v-for="provider in filteredAvailableProviders"
          :key="provider.id"
          class="border border-muted-200 dark:border-muted-800 rounded-lg p-6 hover:border-primary-300 dark:hover:border-primary-700 transition-colors cursor-pointer group"
          @click="() => { selectedIntegration = null; showAddForm = true; }"
        >
          <div class="flex items-start gap-4">
            <div class="flex items-center justify-center size-12 rounded-lg bg-muted-100 dark:bg-muted-800 group-hover:bg-primary-100 dark:group-hover:bg-primary-900 transition-colors">
              <Icon :name="getProviderIcon(provider.id)" class="size-6 text-muted-600 dark:text-muted-400 group-hover:text-primary-600 dark:group-hover:text-primary-400" />
            </div>

            <div class="flex-1 min-w-0">
              <BaseHeading as="h3" size="md" weight="medium" class="mb-1">
                {{ provider.name }}
              </BaseHeading>
              <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400 mb-3">
                {{ provider.description }}
              </BaseParagraph>

              <!-- Model count -->
              <div class="flex items-center gap-2 text-xs text-muted-500 dark:text-muted-400">
                <Icon name="ph:robot" class="size-3" />
                <span>{{ provider.models.length }} models available</span>
              </div>
            </div>
          </div>

          <div class="mt-4 pt-4 border-t border-muted-200 dark:border-muted-800">
            <BaseButton
              size="sm"
              variant="soft"
              class="w-full group-hover:bg-primary-500 group-hover:text-white transition-colors"
            >
              <Icon name="ph:plus" class="size-4" />
              Connect {{ provider.name }}
            </BaseButton>
          </div>
        </div>
      </div>

      <!-- No available providers -->
      <div v-else class="text-center py-12">
        <Icon name="ph:check-circle" class="size-16 mx-auto mb-4 text-success-500" />
        <BaseHeading as="h3" size="lg" weight="medium" class="mb-2">
          All Providers Connected
        </BaseHeading>
        <BaseParagraph class="text-muted-600 dark:text-muted-400">
          You've connected all available LLM providers. Great job!
        </BaseParagraph>
      </div>
    </div>
    <!-- Add/Edit Integration Modal -->
    <BaseModal v-model="showAddForm" size="2xl">
      <template #header>
        <BaseHeading as="h3" size="lg" weight="medium">
          {{ selectedIntegration ? 'Edit Integration' : 'Add New Integration' }}
        </BaseHeading>
      </template>

      <TairoIntegrationForm
        v-if="showAddForm"
        :model-value="selectedIntegration ? {
          name: selectedIntegration.name,
          provider: selectedIntegration.provider,
          apiKey: '', // Don't pre-fill for security
          description: selectedIntegration.description,
          settings: selectedIntegration.settings,
        } : {
          name: '',
          provider: 'openai',
          apiKey: '',
          description: '',
          settings: {},
        }"
        :loading="loading || inheritanceLoading"
        @submit="handleCreateIntegration"
      />

      <template #footer>
        <BaseButton @click="showAddForm = false">
          Cancel
        </BaseButton>
      </template>
    </BaseModal>

    <!-- Settings Modal -->
    <BaseModal v-model="showSettings" size="4xl">
      <template #header>
        <BaseHeading as="h3" size="lg" weight="medium">
          Integration Settings
        </BaseHeading>
      </template>

      <TairoIntegrationSettings
        v-if="selectedIntegration && showSettings"
        :integration="selectedIntegration"
        :show-analytics="true"
        :show-testing="true"
        :show-advanced="true"
        @update:integration="handleUpdateIntegration(selectedIntegration.id, $event)"
        @test-connection="() => {}"
        @validate-settings="() => {}"
        @reset-settings="() => {}"
        @export-config="() => {}"
        @import-config="() => {}"
      />

      <template #footer>
        <BaseButton @click="showSettings = false">
          Close
        </BaseButton>
      </template>
    </BaseModal>
    <!-- Conflict Resolution Modal -->
    <BaseModal v-model="showConflictResolution" size="xl">
      <template #header>
        <BaseHeading as="h3" size="lg" weight="medium">
          Resolve Integration Conflicts
        </BaseHeading>
      </template>

      <div v-if="conflicts.length > 0" class="space-y-4">
        <BaseParagraph class="text-muted-600 dark:text-muted-400">
          The following conflicts were detected in your integrations:
        </BaseParagraph>

        <div
          v-for="{ integration, conflict } in conflicts"
          :key="integration.id"
          class="border border-warning-200 dark:border-warning-800 rounded-lg p-4"
        >
          <div class="flex items-start gap-3">
            <Icon name="ph:warning" class="size-5 text-warning-500 mt-0.5" />
            <div class="flex-1">
              <BaseText size="sm" weight="medium" class="mb-1">
                {{ integration.name }} ({{ getProviderName(integration.provider) }})
              </BaseText>
              <BaseText size="xs" class="text-muted-600 dark:text-muted-400">
                {{ conflict?.message }}
              </BaseText>
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-3 pt-4">
          <BaseButton
            variant="soft"
            @click="showConflictResolution = false"
          >
            Cancel
          </BaseButton>
          <BaseButton
            color="warning"
            :loading="inheritanceLoading"
            @click="handleResolveConflicts"
          >
            Auto-Resolve Conflicts
          </BaseButton>
        </div>
      </div>

      <template #footer>
        <BaseButton @click="showConflictResolution = false">
          Close
        </BaseButton>
      </template>
    </BaseModal>
  </div>
</template>
