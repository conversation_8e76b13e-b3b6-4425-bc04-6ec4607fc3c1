<script setup lang="ts">
import type { Integration, IntegrationFormData, LLMProvider } from '../../types/integration'

definePageMeta({
  title: 'Profile Integrations',
  preview: {
    title: 'Profile - Integrations',
    description: 'Manage profile-specific integrations',
    categories: ['layouts', 'profile'],
    src: '/img/screens/profile-integrations.png',
    srcDark: '/img/screens/profile-integrations-dark.png',
    order: 95,
    new: true,
  },
})

// Composables
const { integrations, loading, error, createIntegration, updateIntegration, deleteIntegration, toggleIntegration, setDefaultIntegration } = useIntegrations({
  includeInherited: true,
})
const { getInheritanceStatus, shouldShowInheritanceUI } = useIntegrationInheritance()
const { currentProfile } = useAuth()
const toaster = useNuiToasts()

// UI state
const showAddModal = ref(false)
const showEditModal = ref(false)
const showDeleteConfirm = ref(false)
const selectedIntegration = ref<Integration | null>(null)
const selectedProvider = ref<LLMProvider | undefined>(undefined)
const isSubmitting = ref(false)

// Search
const searchQuery = ref('')
const filteredIntegrations = computed(() => {
  if (!searchQuery.value)
    return integrations.value

  const query = searchQuery.value.toLowerCase()
  return integrations.value.filter(integration =>
    integration.name.toLowerCase().includes(query)
    || integration.description?.toLowerCase().includes(query)
    || integration.provider.toLowerCase().includes(query),
  )
})

// Handlers
function handleAdd(provider?: LLMProvider) {
  selectedProvider.value = provider
  selectedIntegration.value = null
  showAddModal.value = true
}

function handleEdit(integration: Integration) {
  // Can only edit profile-specific integrations
  const status = getInheritanceStatus(integration, integrations.value)
  if (status.isInherited && !status.isProfileSpecific) {
    toaster.warning('Cannot edit inherited integrations. Create a profile-specific override instead.')
    return
  }

  selectedIntegration.value = integration
  showEditModal.value = true
}

function handleDelete(integration: Integration) {
  // Can only delete profile-specific integrations
  const status = getInheritanceStatus(integration, integrations.value)
  if (status.isInherited && !status.isProfileSpecific) {
    toaster.warning('Cannot delete inherited integrations')
    return
  }

  selectedIntegration.value = integration
  showDeleteConfirm.value = true
}

async function handleToggle(integration: Integration, value: boolean) {
  // Can only toggle profile-specific integrations
  const status = getInheritanceStatus(integration, integrations.value)
  if (status.isInherited && !status.isProfileSpecific) {
    if (!value) {
      // If disabling an inherited integration, create a disabled profile-specific one
      const formData: IntegrationFormData = {
        provider: integration.provider,
        name: `${integration.name} (Profile Override)`,
        description: integration.description,
        apiKey: '', // Will need to be provided
        settings: integration.settings,
        availableToProfiles: false,
      }
      handleAdd(integration.provider)
      return
    }
    toaster.warning('Cannot modify inherited integrations')
    return
  }

  try {
    await toggleIntegration(integration.id, value)
    toaster.success(`Integration ${value ? 'enabled' : 'disabled'}`)
  }
  catch (error) {
    toaster.danger('Failed to update integration')
    console.error(error)
  }
}

async function handleSetDefault(integration: Integration) {
  try {
    await setDefaultIntegration(integration.id, integration.provider)
    toaster.success('Default integration updated')
  }
  catch (error) {
    toaster.danger('Failed to set default integration')
    console.error(error)
  }
}

async function handleSubmitAdd(data: IntegrationFormData) {
  try {
    isSubmitting.value = true
    await createIntegration(data)
    showAddModal.value = false
    toaster.success('Profile integration added successfully')
  }
  catch (error) {
    toaster.danger(error instanceof Error ? error.message : 'Failed to add integration')
  }
  finally {
    isSubmitting.value = false
  }
}

async function handleSubmitEdit(data: IntegrationFormData) {
  if (!selectedIntegration.value)
    return

  try {
    isSubmitting.value = true
    await updateIntegration(selectedIntegration.value.id, {
      name: data.name,
      description: data.description,
      settings: data.settings,
    })
    showEditModal.value = false
    toaster.success('Integration updated successfully')
  }
  catch (error) {
    toaster.danger('Failed to update integration')
  }
  finally {
    isSubmitting.value = false
  }
}

async function confirmDelete() {
  if (!selectedIntegration.value)
    return

  try {
    await deleteIntegration(selectedIntegration.value.id)
    showDeleteConfirm.value = false
    toaster.success('Integration deleted successfully')
  }
  catch (error) {
    toaster.danger('Failed to delete integration')
  }
}
</script>

<template>
  <div class="mt-8 dark:[--color-input-default-bg:var(--color-muted-950)]">
    <!-- Info banner -->
    <div class="border-info-500 bg-info-500/10 rounded-2xl border-2">
      <div class="p-4">
        <div class="gap-6 md:flex md:items-center md:justify-between">
          <BaseAvatar
            rounded="none"
            mask="blob"
            :src="currentProfile?.avatarUrl || '/img/avatars/15.svg'"
            size="lg"
          />
          <div class="max-w-xs flex-1">
            <BaseParagraph weight="semibold" class="text-info-700 dark:text-info-400">
              Profile-Specific Integrations
            </BaseParagraph>
            <BaseParagraph
              size="sm"
              class="text-info-600 dark:text-info-300"
            >
              Configure AI models for this profile. Inherited integrations from your user account are shown with a link icon.
            </BaseParagraph>
          </div>

          <div class="mt-6 flex items-center justify-start gap-3 md:ms-auto md:mt-0 md:justify-end md:space-x-reverse">
            <BaseButton
              variant="primary"
              rounded="md"
              @click="handleAdd()"
            >
              <Icon name="ph:plus" class="size-4" />
              <span>Add Profile Integration</span>
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Header -->
    <div class="mt-8 sm:flex sm:items-center sm:justify-between">
      <div class="space-y-1">
        <BaseHeading
          as="h4"
          size="md"
          weight="medium"
          class="text-muted-900 text-base font-bold dark:text-white"
        >
          Profile Integrations
        </BaseHeading>
        <BaseParagraph
          size="sm"
          weight="medium"
          class="text-muted-500 dark:text-muted-400"
        >
          Manage AI integrations for {{ currentProfile?.displayName || 'this profile' }}.
        </BaseParagraph>
      </div>

      <TairoInput
        v-model="searchQuery"
        icon="lucide:search"
        rounded="md"
        placeholder="Search integrations..."
        class="mt-4 sm:mt-0"
      />
    </div>

    <!-- Inheritance info -->
    <div
      v-if="shouldShowInheritanceUI"
      class="mt-6 rounded-lg bg-info-50 dark:bg-info-950/20 p-4"
    >
      <div class="flex items-start gap-3">
        <Icon name="ph:info-circle" class="size-5 text-info-500 mt-0.5" />
        <div class="flex-1">
          <BaseText size="sm" weight="medium" class="text-info-700 dark:text-info-400">
            Integration Inheritance
          </BaseText>
          <BaseText size="xs" class="text-info-600 dark:text-info-500 mt-1">
            This profile can use integrations from your user account (marked with
            <Icon name="ph:link" class="inline size-3" />).
            You can override them by creating profile-specific integrations.
          </BaseText>
        </div>
      </div>
    </div>

    <!-- Integrations list -->
    <div class="mt-8">
      <TairoIntegrationList
        :integrations="filteredIntegrations"
        :loading="loading"
        :show-inheritance="true"
        @add="handleAdd"
        @edit="handleEdit"
        @delete="handleDelete"
        @toggle="handleToggle"
        @set-default="handleSetDefault"
      />
    </div>

    <!-- Error display -->
    <BaseMessage v-if="error" type="danger" class="mt-4">
      {{ error }}
    </BaseMessage>

    <!-- Add Integration Modal -->
    <TairoModal
      :open="showAddModal"
      size="lg"
      @close="showAddModal = false"
    >
      <template #header>
        <div class="flex items-center gap-2">
          <Icon name="ph:plus-circle" class="size-5" />
          <BaseHeading as="h3" size="md" weight="medium">
            Add Profile Integration
          </BaseHeading>
        </div>
      </template>

      <div class="mb-4 rounded-lg bg-info-50 dark:bg-info-950/20 p-3">
        <BaseText size="sm" class="text-info-700 dark:text-info-400">
          This integration will be specific to the "{{ currentProfile?.displayName }}" profile only.
        </BaseText>
      </div>

      <TairoIntegrationForm
        :provider="selectedProvider"
        :loading="isSubmitting"
        @submit="handleSubmitAdd"
        @cancel="showAddModal = false"
      />
    </TairoModal>

    <!-- Edit Integration Modal -->
    <TairoModal
      :open="showEditModal"
      size="lg"
      @close="showEditModal = false"
    >
      <template #header>
        <div class="flex items-center gap-2">
          <Icon name="ph:pencil" class="size-5" />
          <BaseHeading as="h3" size="md" weight="medium">
            Edit Profile Integration
          </BaseHeading>
        </div>
      </template>

      <TairoIntegrationForm
        v-if="selectedIntegration"
        :provider="selectedIntegration.provider"
        :initial-data="{
          name: selectedIntegration.name,
          description: selectedIntegration.description,
          settings: selectedIntegration.settings,
        }"
        :loading="isSubmitting"
        @submit="handleSubmitEdit"
        @cancel="showEditModal = false"
      />
    </TairoModal>

    <!-- Delete Confirmation Modal -->
    <TairoModal
      :open="showDeleteConfirm"
      size="sm"
      @close="showDeleteConfirm = false"
    >
      <template #header>
        <div class="flex items-center gap-2">
          <Icon name="ph:warning-circle" class="size-5 text-danger-500" />
          <BaseHeading as="h3" size="md" weight="medium">
            Delete Profile Integration
          </BaseHeading>
        </div>
      </template>

      <div class="space-y-4">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          Are you sure you want to delete this profile-specific integration? This action cannot be undone.
        </BaseParagraph>

        <div
          v-if="selectedIntegration"
          class="rounded-lg bg-muted-100 dark:bg-muted-900 p-4"
        >
          <BaseText size="sm" weight="medium">
            {{ selectedIntegration.name }}
          </BaseText>
          <BaseText size="xs" class="text-muted-500 dark:text-muted-400">
            {{ selectedIntegration.provider }}
          </BaseText>
        </div>

        <div class="flex items-center justify-end gap-3 pt-4">
          <BaseButton
            variant="soft"
            @click="showDeleteConfirm = false"
          >
            Cancel
          </BaseButton>
          <BaseButton
            color="danger"
            @click="confirmDelete"
          >
            <Icon name="ph:trash" class="size-4" />
            <span>Delete</span>
          </BaseButton>
        </div>
      </div>
    </TairoModal>
  </div>
</template>
