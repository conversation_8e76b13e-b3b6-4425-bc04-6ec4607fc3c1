<script setup lang="ts">
import type { Integration, IntegrationFormData, LLMProvider } from '~/layers/auth-module/types/integration'
import type { ResolvedIntegration } from '~/layers/auth-module/composables/useIntegrationInheritance'

definePageMeta({
  title: 'Profile Integrations',
  layout: 'sidebar',
})

// Route params
const route = useRoute()
const profileId = computed(() => route.params.id as string)

// Composables
const { currentUser, currentProfile } = useAuth()
const { integrations, loading, error, createIntegration, updateIntegration, deleteIntegration, refreshIntegrations } = useIntegrations()
const { providers, getProviderName, getProviderIcon } = useLLMProviders()
const { 
  loadUserIntegrations,
  loadProfileIntegrations,
  createProfileOverride,
  removeProfileOverride,
  getEffectiveIntegrationsWithStatus,
  detectConflicts,
  resolveConflicts,
  userIntegrations,
  profileIntegrations,
  availableUserIntegrations,
  inheritedIntegrations,
  profileSpecificIntegrations,
  allEffectiveIntegrations,
  integrationConflicts,
  loading: inheritanceLoading,
  error: inheritanceError,
} = useIntegrationInheritance()

// State
const showAddForm = ref(false)
const showSettings = ref(false)
const showOverrideForm = ref(false)
const selectedIntegration = ref<Integration | null>(null)
const selectedUserIntegration = ref<Integration | null>(null)
const searchQuery = ref('')
const selectedProvider = ref<LLMProvider | ''>('')
const showConflictResolution = ref(false)
const activeTab = ref<'inherited' | 'profile-specific'>('inherited')

// Computed properties
const filteredInheritedIntegrations = computed(() => {
  let filtered = inheritedIntegrations.value
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(integration =>
      integration.name.toLowerCase().includes(query) 
      || integration.provider.toLowerCase().includes(query) 
      || integration.description?.toLowerCase().includes(query),
    )
  }
  
  if (selectedProvider.value) {
    filtered = filtered.filter(integration => integration.provider === selectedProvider.value)
  }
  
  return filtered
})

const filteredProfileIntegrations = computed(() => {
  let filtered = profileSpecificIntegrations.value
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(integration =>
      integration.name.toLowerCase().includes(query) 
      || integration.provider.toLowerCase().includes(query) 
      || integration.description?.toLowerCase().includes(query),
    )
  }
  
  if (selectedProvider.value) {
    filtered = filtered.filter(integration => integration.provider === selectedProvider.value)
  }
  
  return filtered
})

const availableProvidersForProfile = computed(() => {
  const usedProviders = new Set([
    ...inheritedIntegrations.value.map(i => i.provider),
    ...profileSpecificIntegrations.value.map(i => i.provider),
  ])
  
  return providers.value.filter(provider => !usedProviders.has(provider.id))
})

const conflicts = computed(() => {
  return integrationConflicts.value
})

const effectiveIntegrations = computed(() => {
  return allEffectiveIntegrations.value
})

// Methods
const handleCreateProfileIntegration = async (data: IntegrationFormData) => {
  try {
    const integrationData = {
      ...data,
      profileId: profileId.value,
      userId: currentUser.value?.uid,
      availableToProfiles: false, // Profile integrations are not available to other profiles
    }
    
    await createIntegration(integrationData)
    showAddForm.value = false
    await refreshData()
  } catch (error) {
    console.error('Failed to create profile integration:', error)
  }
}

const handleCreateOverride = async (data: IntegrationFormData) => {
  try {
    if (!selectedUserIntegration.value) return
    
    await createProfileOverride(
      selectedUserIntegration.value.id,
      profileId.value,
      {
        name: data.name,
        apiKey: data.apiKey,
        description: data.description,
        settings: data.settings,
      },
    )
    
    showOverrideForm.value = false
    selectedUserIntegration.value = null
    await refreshData()
  } catch (error) {
    console.error('Failed to create profile override:', error)
  }
}

const handleRemoveOverride = async (profileIntegrationId: string) => {
  try {
    await removeProfileOverride(profileIntegrationId)
    await refreshData()
  } catch (error) {
    console.error('Failed to remove profile override:', error)
  }
}

const handleUpdateIntegration = async (id: string, updates: Partial<Integration>) => {
  try {
    await updateIntegration(id, updates)
    await refreshData()
  } catch (error) {
    console.error('Failed to update integration:', error)
  }
}

const handleDeleteIntegration = async (id: string) => {
  try {
    await deleteIntegration(id)
    await refreshData()
  } catch (error) {
    console.error('Failed to delete integration:', error)
  }
}

const handleToggleIntegration = async (id: string, active: boolean) => {
  await handleUpdateIntegration(id, { isActive: active })
}

const handleEditIntegration = (integration: Integration) => {
  selectedIntegration.value = integration
  showAddForm.value = true
}

const handleViewSettings = (integration: Integration) => {
  selectedIntegration.value = integration
  showSettings.value = true
}

const handleOverrideInherited = (userIntegration: Integration) => {
  selectedUserIntegration.value = userIntegration
  showOverrideForm.value = true
}

const handleResolveConflicts = async () => {
  try {
    const resolutions = await resolveConflicts(integrations.value)
    await refreshData()
    
    if (resolutions.length > 0) {
      console.log('Conflicts resolved:', resolutions)
    }
  } catch (error) {
    console.error('Failed to resolve conflicts:', error)
  }
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedProvider.value = ''
}

const refreshData = async () => {
  await Promise.all([
    loadUserIntegrations(currentUser.value?.uid || ''),
    loadProfileIntegrations(profileId.value),
    refreshIntegrations(),
  ])
}

// Initialize
onMounted(async () => {
  await refreshData()
})

// Watch for profile changes
watch(() => profileId.value, async () => {
  await refreshData()
})
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <BaseHeading as="h1" size="2xl" weight="bold" class="mb-2">
          Profile Integrations
        </BaseHeading>
        <BaseParagraph class="text-muted-600 dark:text-muted-400">
          Manage integrations for this profile with inheritance from user-level settings
        </BaseParagraph>
      </div>
      
      <div class="flex items-center gap-3">
        <!-- Conflict resolution -->
        <BaseButton
          v-if="conflicts.length > 0"
          variant="soft"
          color="warning"
          @click="showConflictResolution = true"
        >
          <Icon name="ph:warning" class="size-4" />
          {{ conflicts.length }} Conflict{{ conflicts.length > 1 ? 's' : '' }}
        </BaseButton>
        
        <!-- Add profile integration -->
        <BaseButton
          color="primary"
          @click="showAddForm = true"
        >
          <Icon name="ph:plus" class="size-4" />
          Add Profile Integration
        </BaseButton>
      </div>
    </div>

    <!-- Integration Summary -->
    <div class="bg-muted-50 dark:bg-muted-900 rounded-lg p-4">
      <div class="flex items-center gap-3 mb-3">
        <Icon name="ph:hierarchy" class="size-5 text-primary-500" />
        <BaseHeading as="h3" size="md" weight="medium">
          Integration Summary
        </BaseHeading>
      </div>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <span class="text-muted-600 dark:text-muted-400">Inherited:</span>
          <span class="ml-2 font-medium">{{ inheritedIntegrations.length }}</span>
        </div>
        <div>
          <span class="text-muted-600 dark:text-muted-400">Profile-Specific:</span>
          <span class="ml-2 font-medium">{{ profileSpecificIntegrations.length }}</span>
        </div>
        <div>
          <span class="text-muted-600 dark:text-muted-400">Total Effective:</span>
          <span class="ml-2 font-medium">{{ effectiveIntegrations.length }}</span>
        </div>
        <div>
          <span class="text-muted-600 dark:text-muted-400">Conflicts:</span>
          <span class="ml-2 font-medium" :class="conflicts.length > 0 ? 'text-warning-600' : ''">
            {{ conflicts.length }}
          </span>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="flex flex-col sm:flex-row gap-4">
      <div class="flex-1">
        <BaseInput
          v-model="searchQuery"
          icon="ph:magnifying-glass"
          placeholder="Search integrations..."
          shape="curved"
        />
      </div>
      
      <div class="sm:w-48">
        <BaseSelect
          v-model="selectedProvider"
          icon="ph:funnel"
          shape="curved"
        >
          <option value="">
            All Providers
          </option>
          <option
            v-for="provider in providers"
            :key="provider.id"
            :value="provider.id"
          >
            {{ provider.name }}
          </option>
        </BaseSelect>
      </div>
      
      <BaseButton
        v-if="searchQuery || selectedProvider"
        variant="soft"
        @click="clearFilters"
      >
        <Icon name="ph:x" class="size-4" />
        Clear
      </BaseButton>
    </div>

    <!-- Integration Tabs -->
    <BaseTabs v-model="activeTab">
      <BaseTabList>
        <BaseTab value="inherited">
          <Icon name="ph:arrow-down" class="size-4" />
          Inherited ({{ inheritedIntegrations.length }})
        </BaseTab>
        <BaseTab value="profile-specific">
          <Icon name="ph:user" class="size-4" />
          Profile-Specific ({{ profileSpecificIntegrations.length }})
        </BaseTab>
      </BaseTabList>

      <!-- Inherited Integrations Tab -->
      <BaseTabPanel value="inherited">
        <div class="space-y-6">
          <div class="flex items-center justify-between">
            <div>
              <BaseHeading as="h3" size="lg" weight="medium" class="mb-1">
                Inherited Integrations
              </BaseHeading>
              <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                Integrations inherited from user-level settings. You can override these with profile-specific configurations.
              </BaseParagraph>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="loading || inheritanceLoading" class="flex items-center justify-center py-12">
            <div class="flex items-center gap-3">
              <Icon name="ph:spinner" class="size-5 animate-spin text-primary-500" />
              <BaseText>Loading integrations...</BaseText>
            </div>
          </div>

          <!-- Error State -->
          <BaseAlert v-else-if="error || inheritanceError" color="danger" class="mb-6">
            {{ error || inheritanceError }}
          </BaseAlert>

          <!-- Inherited Integrations Grid -->
          <div v-else-if="filteredInheritedIntegrations.length > 0" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div
              v-for="integration in filteredInheritedIntegrations"
              :key="integration.id"
              class="border border-muted-200 dark:border-muted-800 rounded-lg p-6 bg-primary-50/30 dark:bg-primary-950/20"
            >
              <div class="flex items-start justify-between mb-4">
                <div class="flex items-center gap-3">
                  <div class="flex items-center justify-center size-10 rounded-lg bg-muted-100 dark:bg-muted-800">
                    <Icon :name="getProviderIcon(integration.provider)" class="size-5" />
                  </div>
                  <div>
                    <BaseHeading as="h4" size="md" weight="medium" class="mb-1">
                      {{ integration.name }}
                    </BaseHeading>
                    <div class="flex items-center gap-2">
                      <BaseBadge color="primary" size="sm">
                        Inherited
                      </BaseBadge>
                      <BaseText size="xs" class="text-muted-600 dark:text-muted-400">
                        {{ getProviderName(integration.provider) }}
                      </BaseText>
                    </div>
                  </div>
                </div>
                
                <BaseDropdown placement="bottom-end">
                  <BaseButtonIcon size="sm" shape="full">
                    <Icon name="ph:dots-three" />
                  </BaseButtonIcon>

                  <template #content>
                    <BaseDropdownItem @click="handleOverrideInherited(integration)">
                      <Icon name="ph:copy" />
                      <span>Override with Profile Settings</span>
                    </BaseDropdownItem>
                    
                    <BaseDropdownItem @click="handleViewSettings(integration)">
                      <Icon name="ph:gear" />
                      <span>View Settings</span>
                    </BaseDropdownItem>
                  </template>
                </BaseDropdown>
              </div>
              
              <BaseParagraph v-if="integration.description" size="sm" class="text-muted-600 dark:text-muted-400 mb-3">
                {{ integration.description }}
              </BaseParagraph>
              
              <div class="flex items-center justify-between text-xs text-muted-500 dark:text-muted-400">
                <span>Model: {{ integration.settings.defaultModel || 'Not configured' }}</span>
                <span>From user settings</span>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-12">
            <Icon name="ph:arrow-down" class="size-16 mx-auto mb-4 text-muted-400" />
            <BaseHeading as="h3" size="lg" weight="medium" class="mb-2">
              {{ searchQuery || selectedProvider ? 'No matching inherited integrations' : 'No inherited integrations' }}
            </BaseHeading>
            <BaseParagraph class="text-muted-600 dark:text-muted-400">
              {{ 
                searchQuery || selectedProvider 
                  ? 'Try adjusting your search or filters.' 
                  : 'No user-level integrations are available for inheritance.' 
              }}
            </BaseParagraph>
          </div>
        </div>
      </BaseTabPanel>

      <!-- Profile-Specific Integrations Tab -->
      <BaseTabPanel value="profile-specific">
        <div class="space-y-6">
          <div class="flex items-center justify-between">
            <div>
              <BaseHeading as="h3" size="lg" weight="medium" class="mb-1">
                Profile-Specific Integrations
              </BaseHeading>
              <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                Integrations configured specifically for this profile. These override any inherited settings.
              </BaseParagraph>
            </div>
          </div>

          <!-- Profile Integrations Grid -->
          <div v-if="filteredProfileIntegrations.length > 0" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TairoIntegrationCard
              v-for="integration in filteredProfileIntegrations"
              :key="integration.id"
              :integration="integration"
              :show-inheritance="true"
              :show-model-details="true"
              :show-usage-stats="true"
              :show-quick-actions="true"
              @toggle="handleToggleIntegration(integration.id, $event)"
              @edit="handleEditIntegration(integration)"
              @delete="handleDeleteIntegration(integration.id)"
              @test="() => {}"
              @duplicate="() => {}"
              @view-usage="handleViewSettings(integration)"
            >
              <!-- Profile override indicator -->
              <template #badges>
                <BaseBadge color="success" size="sm">
                  Profile Override
                </BaseBadge>
              </template>
              
              <!-- Remove override action -->
              <template #actions>
                <div class="flex items-center gap-2 mt-3 pt-3 border-t border-muted-200 dark:border-muted-800">
                  <BaseButton
                    size="sm"
                    variant="soft"
                    color="danger"
                    @click="handleRemoveOverride(integration.id)"
                  >
                    <Icon name="ph:arrow-counter-clockwise" class="size-3" />
                    Revert to Inherited
                  </BaseButton>
                </div>
              </template>
            </TairoIntegrationCard>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-12">
            <Icon name="ph:user" class="size-16 mx-auto mb-4 text-muted-400" />
            <BaseHeading as="h3" size="lg" weight="medium" class="mb-2">
              {{ searchQuery || selectedProvider ? 'No matching profile integrations' : 'No profile-specific integrations' }}
            </BaseHeading>
            <BaseParagraph class="text-muted-600 dark:text-muted-400 mb-6">
              {{ 
                searchQuery || selectedProvider 
                  ? 'Try adjusting your search or filters.' 
                  : 'Create profile-specific integrations or override inherited ones.' 
              }}
            </BaseParagraph>
            <BaseButton
              v-if="!searchQuery && !selectedProvider"
              color="primary"
              @click="showAddForm = true"
            >
              <Icon name="ph:plus" class="size-4" />
              Add Profile Integration
            </BaseButton>
          </div>
        </div>
      </BaseTabPanel>
    </BaseTabs>

    <!-- Add Profile Integration Modal -->
    <BaseModal v-model="showAddForm" size="2xl">
      <template #header>
        <BaseHeading as="h3" size="lg" weight="medium">
          {{ selectedIntegration ? 'Edit Profile Integration' : 'Add Profile Integration' }}
        </BaseHeading>
      </template>

      <TairoIntegrationForm
        v-if="showAddForm"
        :model-value="selectedIntegration ? {
          name: selectedIntegration.name,
          provider: selectedIntegration.provider,
          apiKey: '', // Don't pre-fill for security
          description: selectedIntegration.description,
          settings: selectedIntegration.settings,
        } : {
          name: '',
          provider: 'openai',
          apiKey: '',
          description: '',
          settings: {},
        }"
        :loading="loading || inheritanceLoading"
        @submit="handleCreateProfileIntegration"
      />

      <template #footer>
        <BaseButton @click="showAddForm = false">
          Cancel
        </BaseButton>
      </template>
    </BaseModal>

    <!-- Override Inherited Integration Modal -->
    <BaseModal v-model="showOverrideForm" size="2xl">
      <template #header>
        <BaseHeading as="h3" size="lg" weight="medium">
          Override Inherited Integration
        </BaseHeading>
      </template>

      <div v-if="selectedUserIntegration" class="space-y-6">
        <BaseAlert color="info">
          <template #icon>
            <Icon name="ph:info" />
          </template>
          You're creating a profile-specific override for "{{ selectedUserIntegration.name }}".
          This will use your own API key and settings instead of the inherited configuration.
        </BaseAlert>

        <TairoIntegrationForm
          :model-value="{
            name: selectedUserIntegration.name + ' (Profile)',
            provider: selectedUserIntegration.provider,
            apiKey: '',
            description: selectedUserIntegration.description,
            settings: { ...selectedUserIntegration.settings },
          }"
          :loading="loading || inheritanceLoading"
          @submit="handleCreateOverride"
        />
      </div>

      <template #footer>
        <BaseButton @click="showOverrideForm = false">
          Cancel
        </BaseButton>
      </template>
    </BaseModal>

    <!-- Settings Modal -->
    <BaseModal v-model="showSettings" size="4xl">
      <template #header>
        <BaseHeading as="h3" size="lg" weight="medium">
          Integration Settings
        </BaseHeading>
      </template>

      <TairoIntegrationSettings
        v-if="selectedIntegration && showSettings"
        :integration="selectedIntegration"
        :show-analytics="true"
        :show-testing="true"
        :show-advanced="true"
        @update:integration="handleUpdateIntegration(selectedIntegration.id, $event)"
        @test-connection="() => {}"
        @validate-settings="() => {}"
        @reset-settings="() => {}"
        @export-config="() => {}"
        @import-config="() => {}"
      />

      <template #footer>
        <BaseButton @click="showSettings = false">
          Close
        </BaseButton>
      </template>
    </BaseModal>

    <!-- Conflict Resolution Modal -->
    <BaseModal v-model="showConflictResolution" size="xl">
      <template #header>
        <BaseHeading as="h3" size="lg" weight="medium">
          Resolve Integration Conflicts
        </BaseHeading>
      </template>

      <div v-if="conflicts.length > 0" class="space-y-4">
        <BaseParagraph class="text-muted-600 dark:text-muted-400">
          The following conflicts were detected in your profile integrations:
        </BaseParagraph>

        <div
          v-for="{ integration, conflict } in conflicts"
          :key="integration.id"
          class="border border-warning-200 dark:border-warning-800 rounded-lg p-4"
        >
          <div class="flex items-start gap-3">
            <Icon name="ph:warning" class="size-5 text-warning-500 mt-0.5" />
            <div class="flex-1">
              <BaseText size="sm" weight="medium" class="mb-1">
                {{ integration.name }} ({{ getProviderName(integration.provider) }})
              </BaseText>
              <BaseText size="xs" class="text-muted-600 dark:text-muted-400">
                {{ conflict?.message }}
              </BaseText>
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-3 pt-4">
          <BaseButton
            variant="soft"
            @click="showConflictResolution = false"
          >
            Cancel
          </BaseButton>
          <BaseButton
            color="warning"
            :loading="inheritanceLoading"
            @click="handleResolveConflicts"
          >
            Auto-Resolve Conflicts
          </BaseButton>
        </div>
      </div>

      <template #footer>
        <BaseButton @click="showConflictResolution = false">
          Close
        </BaseButton>
      </template>
    </BaseModal>
  </div>
</template>
