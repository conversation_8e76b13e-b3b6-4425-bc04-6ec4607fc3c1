<template>
  <div class="container mx-auto p-6 max-w-4xl">
    <div class="mb-8">
      <BaseHeading as="h1" size="2xl" weight="bold" class="mb-2">
        Dynamic Model Fetching Test
      </BaseHeading>
      <BaseParagraph class="text-muted-600 dark:text-muted-400">
        Test the dynamic model fetching system for LLM providers
      </BaseParagraph>
    </div>

    <!-- Provider Selection -->
    <div class="mb-6">
      <BaseLabel>Select Provider</BaseLabel>
      <BaseSelect v-model="selectedProvider" shape="curved" icon="ph:robot">
        <option value="">Select a provider</option>
        <option
          v-for="provider in providers"
          :key="provider.id"
          :value="provider.id"
        >
          {{ provider.name }}
        </option>
      </BaseSelect>
    </div>

    <!-- API Key Input -->
    <div v-if="selectedProvider" class="mb-6">
      <BaseLabel>API Key (for dynamic fetching)</BaseLabel>
      <BaseInput
        v-model="apiKey"
        type="password"
        shape="curved"
        icon="ph:key"
        placeholder="Enter API key to test dynamic fetching"
      />
      <BaseText size="xs" class="text-muted-500 dark:text-muted-400 mt-1">
        Leave empty to see static models only
      </BaseText>
    </div>

    <!-- Provider Info -->
    <div v-if="selectedProvider" class="mb-6">
      <div class="bg-muted-50 dark:bg-muted-900 rounded-lg p-4">
        <div class="flex items-center gap-3 mb-3">
          <Icon :name="getProviderIcon(selectedProvider)" class="size-8" />
          <div>
            <BaseHeading as="h3" size="lg" weight="medium">
              {{ getProviderName(selectedProvider) }}
            </BaseHeading>
            <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
              {{ getProviderDescription(selectedProvider) }}
            </BaseParagraph>
          </div>
        </div>
        
        <div class="flex items-center gap-4 text-sm">
          <div class="flex items-center gap-1">
            <Icon 
              :name="providerSupportsModelFetching(selectedProvider) ? 'ph:check-circle' : 'ph:x-circle'" 
              :class="providerSupportsModelFetching(selectedProvider) ? 'text-success-500' : 'text-muted-400'"
              class="size-4"
            />
            <span>Dynamic Fetching: {{ providerSupportsModelFetching(selectedProvider) ? 'Supported' : 'Not Supported' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div v-if="selectedProvider" class="mb-6 flex gap-3">
      <BaseButton
        @click="fetchModels"
        :loading="loading"
        :disabled="!apiKey"
        color="primary"
      >
        <Icon name="ph:download" class="size-4" />
        Fetch Dynamic Models
      </BaseButton>
      
      <BaseButton
        @click="refreshCache"
        :loading="refreshing"
        :disabled="!apiKey"
        variant="soft"
      >
        <Icon name="ph:arrow-clockwise" class="size-4" />
        Refresh Cache
      </BaseButton>
      
      <BaseButton
        @click="clearProviderCache"
        variant="soft"
        color="danger"
      >
        <Icon name="ph:trash" class="size-4" />
        Clear Cache
      </BaseButton>
    </div>

    <!-- Cache Status -->
    <div v-if="selectedProvider" class="mb-6">
      <BaseCard class="p-4">
        <BaseHeading as="h4" size="md" weight="medium" class="mb-3">
          Cache Status
        </BaseHeading>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span class="text-muted-600 dark:text-muted-400">Has Cache:</span>
            <span class="ml-2 font-medium">{{ cacheStatus?.hasCache ? 'Yes' : 'No' }}</span>
          </div>
          <div>
            <span class="text-muted-600 dark:text-muted-400">Valid:</span>
            <span class="ml-2 font-medium">{{ cacheStatus?.isValid ? 'Yes' : 'No' }}</span>
          </div>
          <div>
            <span class="text-muted-600 dark:text-muted-400">Models:</span>
            <span class="ml-2 font-medium">{{ cacheStatus?.modelCount || 0 }}</span>
          </div>
          <div>
            <span class="text-muted-600 dark:text-muted-400">Last Updated:</span>
            <span class="ml-2 font-medium">
              {{ cacheStatus?.lastFetched ? formatDate(cacheStatus.lastFetched) : 'Never' }}
            </span>
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- Error Display -->
    <div v-if="error" class="mb-6">
      <BaseAlert color="danger" icon="ph:warning">
        {{ error }}
      </BaseAlert>
    </div>

    <!-- Models Display -->
    <div v-if="selectedProvider && models.length > 0">
      <BaseCard class="p-6">
        <div class="flex items-center justify-between mb-4">
          <BaseHeading as="h4" size="lg" weight="medium">
            Available Models ({{ models.length }})
          </BaseHeading>
          <div class="flex items-center gap-2">
            <Icon 
              v-if="isDynamic" 
              name="ph:wifi" 
              class="size-4 text-success-500" 
            />
            <Icon 
              v-else 
              name="ph:wifi-slash" 
              class="size-4 text-muted-400" 
            />
            <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
              {{ isDynamic ? 'Dynamic Models' : 'Static Models' }}
            </BaseText>
          </div>
        </div>

        <div class="space-y-3">
          <div
            v-for="model in models"
            :key="model.id"
            class="border border-muted-200 dark:border-muted-800 rounded-lg p-4"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-2 mb-1">
                  <BaseHeading as="h5" size="sm" weight="medium">
                    {{ model.displayName }}
                  </BaseHeading>
                  <BaseBadge
                    v-if="model.deprecated"
                    color="warning"
                    size="sm"
                  >
                    Deprecated
                  </BaseBadge>
                </div>
                
                <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400 mb-2">
                  ID: {{ model.id }}
                </BaseParagraph>
                
                <div class="flex flex-wrap gap-4 text-xs">
                  <div>
                    <span class="text-muted-600 dark:text-muted-400">Context:</span>
                    <span class="ml-1 font-medium">{{ formatContextWindow(model.contextWindow) }}</span>
                  </div>
                  
                  <div v-if="model.releaseDate">
                    <span class="text-muted-600 dark:text-muted-400">Released:</span>
                    <span class="ml-1 font-medium">{{ model.releaseDate }}</span>
                  </div>
                  
                  <div v-if="model.knowledgeCutoff">
                    <span class="text-muted-600 dark:text-muted-400">Knowledge Cutoff:</span>
                    <span class="ml-1 font-medium">{{ model.knowledgeCutoff }}</span>
                  </div>
                </div>
                
                <div v-if="model.capabilities.length > 0" class="mt-2">
                  <div class="flex flex-wrap gap-1">
                    <BaseBadge
                      v-for="capability in model.capabilities"
                      :key="capability"
                      size="sm"
                      variant="soft"
                    >
                      {{ capability }}
                    </BaseBadge>
                  </div>
                </div>
              </div>
              
              <div v-if="model.pricing" class="text-right text-xs">
                <div class="text-muted-600 dark:text-muted-400">Pricing</div>
                <div class="font-medium">{{ formatPricing(model) }}</div>
              </div>
            </div>
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- No Models Message -->
    <div v-else-if="selectedProvider && !loading">
      <BaseCard class="p-6 text-center">
        <Icon name="ph:robot" class="size-12 mx-auto mb-3 text-muted-400" />
        <BaseHeading as="h4" size="md" weight="medium" class="mb-2">
          No Models Available
        </BaseHeading>
        <BaseParagraph class="text-muted-600 dark:text-muted-400">
          {{ apiKey ? 'Failed to fetch models. Check your API key.' : 'Enter an API key to fetch dynamic models.' }}
        </BaseParagraph>
      </BaseCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { LLMProvider } from '~/layers/auth-module/types/integration'

definePageMeta({
  title: 'Model Fetching Test',
  layout: 'default',
})

// Composables
const {
  providers,
  getProviderName,
  getProviderIcon,
  getProviderDescription,
  getModelsWithApiKey,
  refreshModels,
  formatContextWindow,
  formatPricing,
  providerSupportsModelFetching,
  clearCache,
  getCacheStatus,
} = useLLMProviders()

// State
const selectedProvider = ref<LLMProvider | ''>('')
const apiKey = ref('')
const models = ref<any[]>([])
const loading = ref(false)
const refreshing = ref(false)
const error = ref<string | null>(null)
const isDynamic = ref(false)

// Cache status for selected provider
const cacheStatus = computed(() => {
  if (!selectedProvider.value) return null
  const status = getCacheStatus()
  return status[selectedProvider.value as LLMProvider]
})

// Fetch models
const fetchModels = async () => {
  if (!selectedProvider.value || !apiKey.value) return

  loading.value = true
  error.value = null

  try {
    const fetchedModels = await getModelsWithApiKey(
      selectedProvider.value as LLMProvider,
      apiKey.value
    )
    
    models.value = fetchedModels
    isDynamic.value = true
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to fetch models'
    models.value = []
    isDynamic.value = false
  } finally {
    loading.value = false
  }
}

// Refresh cache
const refreshCache = async () => {
  if (!selectedProvider.value || !apiKey.value) return

  refreshing.value = true
  error.value = null

  try {
    const refreshedModels = await refreshModels(
      selectedProvider.value as LLMProvider,
      apiKey.value,
      { forceRefresh: true }
    )
    
    models.value = refreshedModels
    isDynamic.value = true
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to refresh models'
  } finally {
    refreshing.value = false
  }
}

// Clear cache for selected provider
const clearProviderCache = () => {
  if (!selectedProvider.value) return
  
  clearCache(selectedProvider.value as LLMProvider)
  models.value = []
  isDynamic.value = false
  error.value = null
}

// Format date
const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleString()
}

// Watch provider changes
watch(selectedProvider, () => {
  models.value = []
  error.value = null
  isDynamic.value = false
})

// Watch API key changes
watch(apiKey, () => {
  if (models.value.length > 0) {
    models.value = []
    isDynamic.value = false
  }
})
</script>
