<template>
  <div class="container mx-auto p-6 max-w-6xl">
    <div class="mb-8">
      <BaseHeading as="h1" size="2xl" weight="bold" class="mb-2">
        Enhanced Integration Components Demo
      </BaseHeading>
      <BaseParagraph class="text-muted-600 dark:text-muted-400">
        Showcase of Phase 2 enhanced integration components with dynamic model fetching
      </BaseParagraph>
    </div>

    <!-- Component Showcase Tabs -->
    <BaseTabs v-model="activeTab">
      <BaseTabList>
        <BaseTab value="form">
          <Icon name="ph:plus" class="size-4" />
          Enhanced Form
        </BaseTab>
        <BaseTab value="selector">
          <Icon name="ph:list" class="size-4" />
          Model Selector
        </BaseTab>
        <BaseTab value="cards">
          <Icon name="ph:cards" class="size-4" />
          Integration Cards
        </BaseTab>
        <BaseTab value="settings">
          <Icon name="ph:gear" class="size-4" />
          Settings Panel
        </BaseTab>
      </BaseTabList>

      <!-- Enhanced Form Demo -->
      <BaseTabPanel value="form">
        <div class="space-y-6">
          <BaseCard class="p-6">
            <BaseHeading as="h3" size="lg" weight="medium" class="mb-4">
              Enhanced Integration Form
            </BaseHeading>
            <BaseParagraph class="text-muted-600 dark:text-muted-400 mb-6">
              Features real-time API key validation, dynamic model fetching, and advanced settings.
            </BaseParagraph>
            
            <TairoIntegrationForm
              v-model="formData"
              :loading="formLoading"
              @submit="handleFormSubmit"
            />
          </BaseCard>
        </div>
      </BaseTabPanel>

      <!-- Model Selector Demo -->
      <BaseTabPanel value="selector">
        <div class="space-y-6">
          <!-- Dropdown Mode -->
          <BaseCard class="p-6">
            <BaseHeading as="h3" size="lg" weight="medium" class="mb-4">
              Model Selector - Dropdown Mode
            </BaseHeading>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <BaseLabel>Provider</BaseLabel>
                <BaseSelect v-model="selectorProvider" shape="curved">
                  <option value="openai">OpenAI</option>
                  <option value="anthropic">Anthropic</option>
                  <option value="google">Google</option>
                  <option value="xai">xAI</option>
                </BaseSelect>
              </div>
              
              <div>
                <BaseLabel>API Key (optional)</BaseLabel>
                <BaseInput
                  v-model="selectorApiKey"
                  type="password"
                  placeholder="Enter API key for dynamic models"
                  shape="curved"
                />
              </div>
            </div>
            
            <div class="mt-6">
              <BaseLabel>Selected Model</BaseLabel>
              <TairoModelSelector
                v-model="selectedModel"
                :provider="selectorProvider"
                :api-key="selectorApiKey"
                mode="dropdown"
                :show-pricing="true"
                :show-context="true"
                :show-deprecated="true"
                :enable-filtering="true"
                @model-selected="handleModelSelected"
              />
            </div>
          </BaseCard>

          <!-- List Mode -->
          <BaseCard class="p-6">
            <BaseHeading as="h3" size="lg" weight="medium" class="mb-4">
              Model Selector - List Mode with Comparison
            </BaseHeading>
            
            <TairoModelSelector
              v-model="selectedModel"
              :provider="selectorProvider"
              :api-key="selectorApiKey"
              mode="list"
              :show-pricing="true"
              :show-context="true"
              :show-deprecated="true"
              :show-details="true"
              :enable-filtering="true"
              :enable-comparison="true"
              @model-selected="handleModelSelected"
              @compare-models="handleCompareModels"
            />
          </BaseCard>
        </div>
      </BaseTabPanel>

      <!-- Integration Cards Demo -->
      <BaseTabPanel value="cards">
        <div class="space-y-6">
          <BaseCard class="p-6">
            <BaseHeading as="h3" size="lg" weight="medium" class="mb-4">
              Enhanced Integration Cards
            </BaseHeading>
            <BaseParagraph class="text-muted-600 dark:text-muted-400 mb-6">
              Cards with status indicators, inheritance controls, and quick actions.
            </BaseParagraph>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <TairoIntegrationCard
                v-for="integration in mockIntegrations"
                :key="integration.id"
                :integration="integration"
                :show-inheritance="true"
                :show-model-details="true"
                :show-usage-stats="true"
                :show-quick-actions="true"
                @toggle="handleToggleIntegration(integration.id, $event)"
                @edit="handleEditIntegration(integration.id)"
                @delete="handleDeleteIntegration(integration.id)"
                @test="handleTestIntegration(integration.id)"
                @duplicate="handleDuplicateIntegration(integration.id)"
                @view-usage="handleViewUsage(integration.id)"
              />
            </div>
          </BaseCard>
        </div>
      </BaseTabPanel>

      <!-- Settings Panel Demo -->
      <BaseTabPanel value="settings">
        <div class="space-y-6">
          <BaseCard class="p-6">
            <BaseHeading as="h3" size="lg" weight="medium" class="mb-4">
              Integration Settings Panel
            </BaseHeading>
            <BaseParagraph class="text-muted-600 dark:text-muted-400 mb-6">
              Advanced configuration, testing tools, and usage analytics.
            </BaseParagraph>
            
            <TairoIntegrationSettings
              :integration="mockIntegrations[0]"
              :show-analytics="true"
              :show-testing="true"
              :show-advanced="true"
              @update:integration="handleUpdateIntegration"
              @test-connection="handleTestConnection"
              @validate-settings="handleValidateSettings"
              @reset-settings="handleResetSettings"
              @export-config="handleExportConfig"
              @import-config="handleImportConfig"
            />
          </BaseCard>
        </div>
      </BaseTabPanel>
    </BaseTabs>

    <!-- Comparison Modal -->
    <BaseModal v-model="showComparisonModal" size="4xl">
      <template #header>
        <BaseHeading as="h3" size="lg" weight="medium">
          Model Comparison
        </BaseHeading>
      </template>
      
      <div v-if="comparedModels.length > 0" class="space-y-4">
        <div class="overflow-x-auto">
          <table class="w-full text-sm">
            <thead>
              <tr class="border-b border-muted-200 dark:border-muted-800">
                <th class="text-left p-3">Feature</th>
                <th
                  v-for="model in comparedModels"
                  :key="model.id"
                  class="text-left p-3"
                >
                  {{ model.displayName }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr class="border-b border-muted-200 dark:border-muted-800">
                <td class="p-3 font-medium">Context Window</td>
                <td
                  v-for="model in comparedModels"
                  :key="`${model.id}-context`"
                  class="p-3"
                >
                  {{ formatContextWindow(model.contextWindow) }}
                </td>
              </tr>
              <tr class="border-b border-muted-200 dark:border-muted-800">
                <td class="p-3 font-medium">Capabilities</td>
                <td
                  v-for="model in comparedModels"
                  :key="`${model.id}-capabilities`"
                  class="p-3"
                >
                  <div class="flex flex-wrap gap-1">
                    <BaseBadge
                      v-for="capability in model.capabilities"
                      :key="capability"
                      size="sm"
                      variant="soft"
                    >
                      {{ capability }}
                    </BaseBadge>
                  </div>
                </td>
              </tr>
              <tr class="border-b border-muted-200 dark:border-muted-800">
                <td class="p-3 font-medium">Release Date</td>
                <td
                  v-for="model in comparedModels"
                  :key="`${model.id}-date`"
                  class="p-3"
                >
                  {{ model.releaseDate || 'Unknown' }}
                </td>
              </tr>
              <tr v-if="comparedModels.some(m => m.pricing)">
                <td class="p-3 font-medium">Pricing</td>
                <td
                  v-for="model in comparedModels"
                  :key="`${model.id}-pricing`"
                  class="p-3"
                >
                  {{ model.pricing ? formatPricing(model) : 'N/A' }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <template #footer>
        <BaseButton @click="showComparisonModal = false">
          Close
        </BaseButton>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import type { IntegrationFormData, Integration, LLMProvider, LLMModel } from '~/layers/auth-module/types/integration'

definePageMeta({
  title: 'Enhanced Components Demo',
  layout: 'default',
})

// Composables
const { formatContextWindow, formatPricing } = useLLMProviders()

// State
const activeTab = ref('form')
const formLoading = ref(false)
const selectorProvider = ref<LLMProvider>('openai')
const selectorApiKey = ref('')
const selectedModel = ref<string>()
const comparedModels = ref<LLMModel[]>([])
const showComparisonModal = ref(false)

// Form data
const formData = ref<IntegrationFormData>({
  name: '',
  provider: 'openai',
  apiKey: '',
  description: '',
  settings: {},
})

// Mock integrations for demo
const mockIntegrations = ref<Integration[]>([
  {
    id: '1',
    userId: 'user1',
    provider: 'openai',
    name: 'OpenAI GPT-4',
    description: 'Primary OpenAI integration for advanced tasks',
    credentials: {
      apiKey: 'sk-***',
      encryptedAt: { toMillis: () => Date.now() } as any,
    },
    settings: {
      defaultModel: 'gpt-4-turbo',
      maxTokens: 4096,
      temperature: 0.7,
    },
    isActive: true,
    isDefault: true,
    availableToProfiles: true,
    lastUsedAt: { toMillis: () => Date.now() - 3600000, toDate: () => new Date(Date.now() - 3600000) } as any,
    createdAt: { toMillis: () => Date.now() - 86400000, toDate: () => new Date(Date.now() - 86400000) } as any,
    updatedAt: { toMillis: () => Date.now() - 3600000, toDate: () => new Date(Date.now() - 3600000) } as any,
  },
  {
    id: '2',
    userId: 'user1',
    provider: 'anthropic',
    name: 'Claude Assistant',
    description: 'Anthropic Claude for analysis and writing',
    credentials: {
      apiKey: 'sk-ant-***',
      encryptedAt: { toMillis: () => Date.now() } as any,
    },
    settings: {
      defaultModel: 'claude-3-5-sonnet-20241022',
      maxTokens: 8192,
      temperature: 0.5,
    },
    isActive: false,
    isDefault: false,
    availableToProfiles: false,
    lastUsedAt: { toMillis: () => Date.now() - 86400000 * 7, toDate: () => new Date(Date.now() - 86400000 * 7) } as any,
    createdAt: { toMillis: () => Date.now() - 86400000 * 14, toDate: () => new Date(Date.now() - 86400000 * 14) } as any,
    updatedAt: { toMillis: () => Date.now() - 86400000 * 7, toDate: () => new Date(Date.now() - 86400000 * 7) } as any,
  },
])

// Event handlers
const handleFormSubmit = async (data: IntegrationFormData) => {
  formLoading.value = true
  try {
    console.log('Form submitted:', data)
    await new Promise(resolve => setTimeout(resolve, 2000))
    // Handle form submission
  } finally {
    formLoading.value = false
  }
}

const handleModelSelected = (model: LLMModel) => {
  console.log('Model selected:', model)
}

const handleCompareModels = (models: LLMModel[]) => {
  comparedModels.value = models
  if (models.length > 0) {
    showComparisonModal.value = true
  }
}

const handleToggleIntegration = (id: string, active: boolean) => {
  console.log('Toggle integration:', id, active)
}

const handleEditIntegration = (id: string) => {
  console.log('Edit integration:', id)
}

const handleDeleteIntegration = (id: string) => {
  console.log('Delete integration:', id)
}

const handleTestIntegration = (id: string) => {
  console.log('Test integration:', id)
}

const handleDuplicateIntegration = (id: string) => {
  console.log('Duplicate integration:', id)
}

const handleViewUsage = (id: string) => {
  console.log('View usage:', id)
}

const handleUpdateIntegration = (updates: Partial<Integration>) => {
  console.log('Update integration:', updates)
}

const handleTestConnection = () => {
  console.log('Test connection')
}

const handleValidateSettings = () => {
  console.log('Validate settings')
}

const handleResetSettings = () => {
  console.log('Reset settings')
}

const handleExportConfig = () => {
  console.log('Export config')
}

const handleImportConfig = (config: any) => {
  console.log('Import config:', config)
}
</script>
