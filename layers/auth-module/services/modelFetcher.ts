import type { <PERSON><PERSON><PERSON><PERSON>, LLMModel, ModelFetchResult, ModelFetchOptions } from '../types/integration'
import { getLLMProvider } from '../config/llm-providers'

/**
 * Service class for fetching models from LLM providers
 * Handles provider-specific authentication and endpoint handling
 */
export class ModelFetcher {
  private static readonly DEFAULT_TIMEOUT = 10000 // 10 seconds
  private static readonly RETRY_ATTEMPTS = 2

  /**
   * Fetch models from a specific LLM provider
   */
  async fetchModels(
    provider: LLMProvider, 
    apiKey: string, 
    options: ModelFetchOptions = {}
  ): Promise<ModelFetchResult> {
    const { timeout = ModelFetcher.DEFAULT_TIMEOUT, fallbackToStatic = true } = options

    try {
      const providerConfig = getLLMProvider(provider)
      if (!providerConfig) {
        throw new Error(`Unknown provider: ${provider}`)
      }

      let models: LLMModel[]
      
      try {
        // Attempt to fetch from API
        models = await this.fetchFromProvider(provider, apiKey, timeout)
      } catch (apiError) {
        console.warn(`Failed to fetch models from ${provider} API:`, apiError)
        
        if (fallbackToStatic) {
          // Fallback to static models
          models = providerConfig.models
          console.info(`Using static models for ${provider}`)
        } else {
          throw apiError
        }
      }

      return {
        success: true,
        models,
        fromCache: false,
        lastUpdated: Date.now(),
      }
    } catch (error) {
      console.error(`Error fetching models for ${provider}:`, error)
      
      return {
        success: false,
        models: [],
        error: error instanceof Error ? error.message : 'Unknown error',
        fromCache: false,
      }
    }
  }

  /**
   * Fetch models from provider API with retries
   */
  private async fetchFromProvider(
    provider: LLMProvider, 
    apiKey: string, 
    timeout: number
  ): Promise<LLMModel[]> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= ModelFetcher.RETRY_ATTEMPTS; attempt++) {
      try {
        switch (provider) {
          case 'openai':
            return await this.fetchOpenAIModels(apiKey, timeout)
          case 'anthropic':
            return await this.fetchAnthropicModels(apiKey, timeout)
          case 'google':
            return await this.fetchGoogleModels(apiKey, timeout)
          case 'xai':
            return await this.fetchXAIModels(apiKey, timeout)
          default:
            throw new Error(`Unsupported provider: ${provider}`)
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error')
        
        if (attempt < ModelFetcher.RETRY_ATTEMPTS) {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
        }
      }
    }

    throw lastError || new Error('All retry attempts failed')
  }

  /**
   * Fetch models from OpenAI API
   */
  private async fetchOpenAIModels(apiKey: string, timeout: number): Promise<LLMModel[]> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      })

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      
      // Filter and transform OpenAI models
      return data.data
        .filter((model: any) => model.id.includes('gpt'))
        .map((model: any) => this.transformOpenAIModel(model))
        .sort((a: LLMModel, b: LLMModel) => b.releaseDate?.localeCompare(a.releaseDate || '') || 0)
    } finally {
      clearTimeout(timeoutId)
    }
  }

  /**
   * Fetch models from Anthropic API
   */
  private async fetchAnthropicModels(apiKey: string, timeout: number): Promise<LLMModel[]> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch('https://api.anthropic.com/v1/models', {
        headers: {
          'x-api-key': apiKey,
          'anthropic-version': '2023-06-01',
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      })

      if (!response.ok) {
        throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      
      // Transform Anthropic models
      return data.data
        .filter((model: any) => model.type === 'model')
        .map((model: any) => this.transformAnthropicModel(model))
        .sort((a: LLMModel, b: LLMModel) => b.releaseDate?.localeCompare(a.releaseDate || '') || 0)
    } finally {
      clearTimeout(timeoutId)
    }
  }

  /**
   * Fetch models from Google Gemini API
   */
  private async fetchGoogleModels(apiKey: string, timeout: number): Promise<LLMModel[]> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      })

      if (!response.ok) {
        throw new Error(`Google API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      
      // Transform Google models
      return data.models
        .filter((model: any) => model.name?.includes('gemini'))
        .map((model: any) => this.transformGoogleModel(model))
        .sort((a: LLMModel, b: LLMModel) => b.releaseDate?.localeCompare(a.releaseDate || '') || 0)
    } finally {
      clearTimeout(timeoutId)
    }
  }

  /**
   * Fetch models from xAI API
   */
  private async fetchXAIModels(apiKey: string, timeout: number): Promise<LLMModel[]> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch('https://api.x.ai/v1/models', {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      })

      if (!response.ok) {
        throw new Error(`xAI API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      
      // Transform xAI models
      return data.data
        .filter((model: any) => model.id.includes('grok'))
        .map((model: any) => this.transformXAIModel(model))
        .sort((a: LLMModel, b: LLMModel) => b.releaseDate?.localeCompare(a.releaseDate || '') || 0)
    } finally {
      clearTimeout(timeoutId)
    }
  }

  /**
   * Transform OpenAI model data to our standard format
   */
  private transformOpenAIModel(model: any): LLMModel {
    const capabilities = ['chat']
    
    // Add capabilities based on model name
    if (model.id.includes('gpt-4')) {
      capabilities.push('function-calling')
      if (model.id.includes('vision') || model.id.includes('turbo')) {
        capabilities.push('vision')
      }
    }
    if (model.id.includes('gpt-3.5')) {
      capabilities.push('function-calling')
    }

    return {
      id: model.id,
      name: model.id,
      displayName: this.formatDisplayName(model.id),
      contextWindow: this.getContextWindow(model.id),
      capabilities,
      releaseDate: this.extractReleaseDate(model.id),
    }
  }

  /**
   * Transform Anthropic model data to our standard format
   */
  private transformAnthropicModel(model: any): LLMModel {
    return {
      id: model.id,
      name: model.id,
      displayName: model.display_name || this.formatDisplayName(model.id),
      contextWindow: model.max_tokens || 200000,
      capabilities: model.capabilities || ['chat', 'vision'],
      releaseDate: this.extractReleaseDate(model.id),
    }
  }

  /**
   * Transform Google model data to our standard format
   */
  private transformGoogleModel(model: any): LLMModel {
    const modelId = model.name?.replace('models/', '') || model.name
    
    return {
      id: modelId,
      name: modelId,
      displayName: model.displayName || this.formatDisplayName(modelId),
      contextWindow: model.inputTokenLimit || 1000000,
      capabilities: model.supportedGenerationMethods || ['chat', 'vision', 'function-calling', 'multimodal'],
      releaseDate: this.extractReleaseDate(modelId),
    }
  }

  /**
   * Transform xAI model data to our standard format
   */
  private transformXAIModel(model: any): LLMModel {
    return {
      id: model.id,
      name: model.id,
      displayName: this.formatDisplayName(model.id),
      contextWindow: model.context_length || 131072,
      capabilities: ['chat', 'function-calling'],
      releaseDate: this.extractReleaseDate(model.id),
    }
  }

  /**
   * Format model ID into a human-readable display name
   */
  private formatDisplayName(modelId: string): string {
    return modelId
      .replace(/[-_]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .replace(/Gpt/g, 'GPT')
      .replace(/Claude/g, 'Claude')
      .replace(/Gemini/g, 'Gemini')
      .replace(/Grok/g, 'Grok')
  }

  /**
   * Get context window size based on model ID
   */
  private getContextWindow(modelId: string): number {
    if (modelId.includes('gpt-4-turbo') || modelId.includes('gpt-4o')) return 128000
    if (modelId.includes('gpt-4-32k')) return 32768
    if (modelId.includes('gpt-4')) return 8192
    if (modelId.includes('gpt-3.5')) return 16384
    return 4096 // Default fallback
  }

  /**
   * Extract release date from model ID
   */
  private extractReleaseDate(modelId: string): string | undefined {
    // Extract date patterns like YYYYMMDD or YYYY-MM-DD
    const dateMatch = modelId.match(/(\d{4})-?(\d{2})-?(\d{2})/)
    if (dateMatch) {
      return `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`
    }
    
    // Fallback to current date for new models
    if (modelId.includes('turbo') || modelId.includes('latest')) {
      return new Date().toISOString().split('T')[0]
    }
    
    return undefined
  }
}

// Export singleton instance
export const modelFetcher = new ModelFetcher()
