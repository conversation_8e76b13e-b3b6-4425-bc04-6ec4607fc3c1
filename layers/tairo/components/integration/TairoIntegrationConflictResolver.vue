<script setup lang="ts">
import type { Integration, LLMProvider } from '~/layers/auth-module/types/integration'
import type { IntegrationInheritanceStatus } from '~/layers/auth-module/composables/useIntegrationInheritance'

/**
 * Integration conflict resolution component
 * Handles detection and resolution of integration conflicts
 */

interface ConflictItem {
  integration: Integration
  conflict: IntegrationInheritanceStatus['conflict']
  status: IntegrationInheritanceStatus
}

interface Props {
  /** List of integrations with conflicts */
  conflicts: ConflictItem[]
  /** Loading state */
  loading?: boolean
  /** Show detailed conflict information */
  showDetails?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showDetails: true,
})

const emit = defineEmits<{
  'resolve-conflict': [integrationId: string, resolution: string]
  'resolve-all': []
  'dismiss-conflict': [integrationId: string]
}>()

// Composables
const { getProviderName, getProviderIcon } = useLLMProviders()

// State
const selectedResolutions = ref<Record<string, string>>({})
const showAdvancedOptions = ref(false)

// Computed properties
const conflictsByType = computed(() => {
  const grouped: Record<string, ConflictItem[]> = {}
  
  props.conflicts.forEach(conflict => {
    const type = conflict.conflict?.type || 'unknown'
    if (!grouped[type]) {
      grouped[type] = []
    }
    grouped[type].push(conflict)
  })
  
  return grouped
})

const conflictTypeLabels = computed(() => ({
  duplicate_provider: 'Duplicate Provider',
  conflicting_settings: 'Conflicting Settings',
  deprecated_parent: 'Deprecated Parent',
  unknown: 'Unknown Conflict',
}))

const conflictTypeDescriptions = computed(() => ({
  duplicate_provider: 'Multiple active integrations for the same provider',
  conflicting_settings: 'Profile settings conflict with inherited settings',
  deprecated_parent: 'Parent integration is no longer active',
  unknown: 'An unknown conflict was detected',
}))

const resolutionOptions = computed(() => ({
  duplicate_provider: [
    { value: 'keep_profile', label: 'Keep Profile Integration', description: 'Deactivate user-level integration' },
    { value: 'keep_user', label: 'Keep User Integration', description: 'Remove profile override' },
    { value: 'keep_newest', label: 'Keep Newest', description: 'Automatically choose the most recent' },
  ],
  conflicting_settings: [
    { value: 'use_profile', label: 'Use Profile Settings', description: 'Override with profile configuration' },
    { value: 'use_inherited', label: 'Use Inherited Settings', description: 'Revert to user-level settings' },
    { value: 'merge_settings', label: 'Merge Settings', description: 'Combine compatible settings' },
  ],
  deprecated_parent: [
    { value: 'create_independent', label: 'Make Independent', description: 'Convert to standalone profile integration' },
    { value: 'deactivate', label: 'Deactivate', description: 'Disable this integration' },
    { value: 'find_alternative', label: 'Find Alternative', description: 'Suggest alternative parent integration' },
  ],
}))

const canResolveAll = computed(() => {
  return props.conflicts.every(conflict => 
    selectedResolutions.value[conflict.integration.id] || 
    getDefaultResolution(conflict.conflict?.type || 'unknown')
  )
})

// Methods
const getConflictIcon = (type: string) => {
  switch (type) {
    case 'duplicate_provider':
      return 'ph:copy'
    case 'conflicting_settings':
      return 'ph:gear'
    case 'deprecated_parent':
      return 'ph:warning'
    default:
      return 'ph:question'
  }
}

const getConflictColor = (severity: string) => {
  switch (severity) {
    case 'error':
      return 'danger'
    case 'warning':
      return 'warning'
    default:
      return 'muted'
  }
}

const getDefaultResolution = (type: string): string => {
  switch (type) {
    case 'duplicate_provider':
      return 'keep_profile'
    case 'conflicting_settings':
      return 'use_profile'
    case 'deprecated_parent':
      return 'create_independent'
    default:
      return ''
  }
}

const selectResolution = (integrationId: string, resolution: string) => {
  selectedResolutions.value[integrationId] = resolution
}

const resolveConflict = (integrationId: string) => {
  const resolution = selectedResolutions.value[integrationId]
  if (resolution) {
    emit('resolve-conflict', integrationId, resolution)
  }
}

const resolveAllConflicts = () => {
  // Set default resolutions for conflicts without selected resolutions
  props.conflicts.forEach(conflict => {
    if (!selectedResolutions.value[conflict.integration.id]) {
      selectedResolutions.value[conflict.integration.id] = getDefaultResolution(conflict.conflict?.type || 'unknown')
    }
  })
  
  emit('resolve-all')
}

const dismissConflict = (integrationId: string) => {
  emit('dismiss-conflict', integrationId)
}

// Initialize default resolutions
onMounted(() => {
  props.conflicts.forEach(conflict => {
    if (!selectedResolutions.value[conflict.integration.id]) {
      selectedResolutions.value[conflict.integration.id] = getDefaultResolution(conflict.conflict?.type || 'unknown')
    }
  })
})
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <BaseHeading as="h3" size="lg" weight="medium" class="mb-1">
          Integration Conflicts
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          {{ conflicts.length }} conflict{{ conflicts.length > 1 ? 's' : '' }} detected that need{{ conflicts.length === 1 ? 's' : '' }} resolution
        </BaseParagraph>
      </div>
      
      <div class="flex items-center gap-3">
        <BaseButton
          v-if="conflicts.length > 1"
          variant="soft"
          @click="showAdvancedOptions = !showAdvancedOptions"
        >
          <Icon name="ph:sliders" class="size-4" />
          {{ showAdvancedOptions ? 'Hide' : 'Show' }} Advanced
        </BaseButton>
        
        <BaseButton
          color="primary"
          :disabled="!canResolveAll"
          :loading="loading"
          @click="resolveAllConflicts"
        >
          <Icon name="ph:check" class="size-4" />
          Resolve All
        </BaseButton>
      </div>
    </div>

    <!-- Conflict Summary -->
    <div v-if="showAdvancedOptions" class="bg-muted-50 dark:bg-muted-900 rounded-lg p-4">
      <BaseHeading as="h4" size="md" weight="medium" class="mb-3">
        Conflict Summary
      </BaseHeading>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div
          v-for="(conflictList, type) in conflictsByType"
          :key="type"
          class="flex items-center gap-3"
        >
          <div class="flex items-center justify-center size-8 rounded-lg bg-muted-100 dark:bg-muted-800">
            <Icon :name="getConflictIcon(type)" class="size-4 text-muted-600 dark:text-muted-400" />
          </div>
          <div>
            <BaseText size="sm" weight="medium">
              {{ conflictTypeLabels[type] }}
            </BaseText>
            <BaseText size="xs" class="text-muted-600 dark:text-muted-400">
              {{ conflictList.length }} conflict{{ conflictList.length > 1 ? 's' : '' }}
            </BaseText>
          </div>
        </div>
      </div>
    </div>

    <!-- Conflicts List -->
    <div class="space-y-4">
      <div
        v-for="conflict in conflicts"
        :key="conflict.integration.id"
        class="border rounded-lg p-6"
        :class="[
          conflict.conflict?.severity === 'error' 
            ? 'border-danger-200 dark:border-danger-800 bg-danger-50/50 dark:bg-danger-950/20'
            : 'border-warning-200 dark:border-warning-800 bg-warning-50/50 dark:bg-warning-950/20'
        ]"
      >
        <div class="flex items-start gap-4">
          <!-- Integration Info -->
          <div class="flex items-center gap-3 flex-1">
            <div class="flex items-center justify-center size-10 rounded-lg bg-muted-100 dark:bg-muted-800">
              <Icon :name="getProviderIcon(conflict.integration.provider)" class="size-5" />
            </div>
            
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-1">
                <BaseHeading as="h4" size="md" weight="medium">
                  {{ conflict.integration.name }}
                </BaseHeading>
                <BaseBadge
                  :color="getConflictColor(conflict.conflict?.severity || 'warning')"
                  size="sm"
                >
                  {{ conflictTypeLabels[conflict.conflict?.type || 'unknown'] }}
                </BaseBadge>
              </div>
              
              <BaseText size="sm" class="text-muted-600 dark:text-muted-400 mb-2">
                {{ getProviderName(conflict.integration.provider) }} • {{ conflict.conflict?.message }}
              </BaseText>
              
              <div v-if="showDetails" class="text-xs text-muted-500 dark:text-muted-400">
                <span>Source: {{ conflict.status.source }}</span>
                <span class="mx-2">•</span>
                <span>{{ conflict.status.isInherited ? 'Inherited' : 'Direct' }}</span>
                <span v-if="conflict.status.isOverridden" class="mx-2">• Overridden</span>
              </div>
            </div>
          </div>

          <!-- Resolution Options -->
          <div class="w-80">
            <BaseLabel class="mb-2">Resolution</BaseLabel>
            <BaseSelect
              :model-value="selectedResolutions[conflict.integration.id]"
              @update:model-value="selectResolution(conflict.integration.id, $event)"
              size="sm"
            >
              <option
                v-for="option in resolutionOptions[conflict.conflict?.type || 'unknown'] || []"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </option>
            </BaseSelect>
            
            <BaseText
              v-if="selectedResolutions[conflict.integration.id]"
              size="xs"
              class="text-muted-600 dark:text-muted-400 mt-1"
            >
              {{ resolutionOptions[conflict.conflict?.type || 'unknown']?.find(o => o.value === selectedResolutions[conflict.integration.id])?.description }}
            </BaseText>
          </div>

          <!-- Actions -->
          <div class="flex items-center gap-2">
            <BaseButton
              size="sm"
              variant="soft"
              color="primary"
              :disabled="!selectedResolutions[conflict.integration.id]"
              @click="resolveConflict(conflict.integration.id)"
            >
              <Icon name="ph:check" class="size-3" />
              Resolve
            </BaseButton>
            
            <BaseButton
              size="sm"
              variant="soft"
              @click="dismissConflict(conflict.integration.id)"
            >
              <Icon name="ph:x" class="size-3" />
              Dismiss
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="conflicts.length === 0" class="text-center py-12">
      <Icon name="ph:check-circle" class="size-16 mx-auto mb-4 text-success-500" />
      <BaseHeading as="h3" size="lg" weight="medium" class="mb-2">
        No Conflicts Detected
      </BaseHeading>
      <BaseParagraph class="text-muted-600 dark:text-muted-400">
        All integrations are properly configured without conflicts.
      </BaseParagraph>
    </div>
  </div>
</template>
