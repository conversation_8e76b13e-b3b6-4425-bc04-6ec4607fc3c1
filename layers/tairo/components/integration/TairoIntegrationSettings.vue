<script setup lang="ts">
import type { Integration, LLMModel } from '~/layers/auth-module/types/integration'

/**
 * Advanced integration settings panel with configuration, analytics, and testing tools
 */

interface Props {
  /** Integration to configure */
  integration: Integration
  /** Show usage analytics */
  showAnalytics?: boolean
  /** Show testing tools */
  showTesting?: boolean
  /** Show advanced configuration */
  showAdvanced?: boolean
  /** Loading state */
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showAnalytics: true,
  showTesting: true,
  showAdvanced: true,
  loading: false,
})

const emit = defineEmits<{
  'update:integration': [integration: Partial<Integration>]
  'test-connection': []
  'validate-settings': []
  'reset-settings': []
  'export-config': []
  'import-config': [config: any]
}>()

// Composables
const { 
  getModels, 
  formatContextWindow, 
  formatPricing,
  getProviderName,
  getProviderIcon
} = useLLMProviders()

// State
const activeTab = ref('general')
const testingConnection = ref(false)
const testResult = ref<{ success: boolean; message: string; latency?: number } | null>(null)
const validatingSettings = ref(false)
const validationErrors = ref<string[]>([])

// Local settings copy for editing
const localSettings = ref({
  ...props.integration.settings,
})

// Available models for the provider
const availableModels = computed(() => getModels(props.integration.provider))

// Selected model details
const selectedModel = computed(() => {
  if (!localSettings.value.defaultModel) return null
  return availableModels.value.find(m => m.id === localSettings.value.defaultModel)
})

// Settings validation
const settingsValid = computed(() => {
  return validationErrors.value.length === 0
})

// Mock usage analytics (in real app, this would come from API)
const usageAnalytics = computed(() => ({
  totalRequests: 1247,
  successRate: 98.5,
  averageLatency: 245,
  totalTokens: 156789,
  estimatedCost: 23.45,
  lastWeekRequests: [45, 67, 89, 123, 98, 76, 54],
  topModels: [
    { model: 'gpt-4-turbo', requests: 567, percentage: 45.5 },
    { model: 'gpt-3.5-turbo', requests: 423, percentage: 33.9 },
    { model: 'gpt-4', requests: 257, percentage: 20.6 },
  ],
}))

// Methods
const updateSettings = (key: string, value: any) => {
  localSettings.value = {
    ...localSettings.value,
    [key]: value,
  }
  
  emit('update:integration', {
    settings: localSettings.value,
  })
}

const testConnection = async () => {
  testingConnection.value = true
  testResult.value = null
  
  try {
    const startTime = Date.now()
    
    // Emit test event and wait for result
    emit('test-connection')
    
    // Simulate test (in real app, this would be handled by parent)
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const latency = Date.now() - startTime
    
    testResult.value = {
      success: true,
      message: 'Connection successful',
      latency,
    }
  } catch (error) {
    testResult.value = {
      success: false,
      message: error instanceof Error ? error.message : 'Connection failed',
    }
  } finally {
    testingConnection.value = false
  }
}

const validateSettings = async () => {
  validatingSettings.value = true
  validationErrors.value = []
  
  try {
    // Basic validation
    const errors: string[] = []
    
    if (localSettings.value.maxTokens && localSettings.value.maxTokens < 1) {
      errors.push('Max tokens must be greater than 0')
    }
    
    if (localSettings.value.temperature !== undefined) {
      if (localSettings.value.temperature < 0 || localSettings.value.temperature > 2) {
        errors.push('Temperature must be between 0 and 2')
      }
    }
    
    if (localSettings.value.baseUrl && !isValidUrl(localSettings.value.baseUrl)) {
      errors.push('Base URL must be a valid URL')
    }
    
    validationErrors.value = errors
    emit('validate-settings')
  } finally {
    validatingSettings.value = false
  }
}

const resetSettings = () => {
  localSettings.value = {
    defaultModel: undefined,
    maxTokens: undefined,
    temperature: undefined,
    baseUrl: undefined,
  }
  
  emit('reset-settings')
}

const exportConfig = () => {
  const config = {
    integration: props.integration,
    settings: localSettings.value,
    exportedAt: new Date().toISOString(),
  }
  
  emit('export-config')
  
  // Download as JSON file
  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${props.integration.name}-config.json`
  a.click()
  URL.revokeObjectURL(url)
}

const importConfig = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target?.result as string)
      if (config.settings) {
        localSettings.value = { ...config.settings }
        emit('import-config', config)
      }
    } catch (error) {
      console.error('Failed to import config:', error)
    }
  }
  reader.readAsText(file)
}

const isValidUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Watch for external changes
watch(() => props.integration.settings, (newSettings) => {
  localSettings.value = { ...newSettings }
}, { deep: true })

// Validate settings on mount and when they change
onMounted(() => {
  validateSettings()
})

watch(localSettings, () => {
  validateSettings()
}, { deep: true })
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <div class="flex items-center justify-center size-10 rounded-lg bg-muted-100 dark:bg-muted-800">
          <Icon :name="getProviderIcon(integration.provider)" class="size-5" />
        </div>
        <div>
          <BaseHeading as="h3" size="lg" weight="medium">
            {{ integration.name }}
          </BaseHeading>
          <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
            {{ getProviderName(integration.provider) }} Integration Settings
          </BaseText>
        </div>
      </div>
      
      <div class="flex items-center gap-2">
        <BaseButton
          size="sm"
          variant="soft"
          @click="exportConfig"
          :disabled="loading"
        >
          <Icon name="ph:download" class="size-4" />
          Export
        </BaseButton>
        
        <BaseButton
          size="sm"
          variant="soft"
          @click="$refs.importInput?.click()"
          :disabled="loading"
        >
          <Icon name="ph:upload" class="size-4" />
          Import
        </BaseButton>
        
        <input
          ref="importInput"
          type="file"
          accept=".json"
          class="hidden"
          @change="importConfig"
        />
      </div>
    </div>

    <!-- Tabs -->
    <BaseTabs v-model="activeTab">
      <BaseTabList>
        <BaseTab value="general">
          <Icon name="ph:gear" class="size-4" />
          General
        </BaseTab>
        <BaseTab v-if="showAdvanced" value="advanced">
          <Icon name="ph:sliders" class="size-4" />
          Advanced
        </BaseTab>
        <BaseTab v-if="showTesting" value="testing">
          <Icon name="ph:test-tube" class="size-4" />
          Testing
        </BaseTab>
        <BaseTab v-if="showAnalytics" value="analytics">
          <Icon name="ph:chart-line" class="size-4" />
          Analytics
        </BaseTab>
      </BaseTabList>

      <!-- General Settings -->
      <BaseTabPanel value="general">
        <div class="space-y-6">
          <!-- Model Selection -->
          <div>
            <BaseLabel>Default Model</BaseLabel>
            <TairoModelSelector
              :model-value="localSettings.defaultModel"
              :provider="integration.provider"
              mode="dropdown"
              :show-pricing="true"
              :show-context="true"
              :show-deprecated="true"
              :enable-filtering="true"
              @update:model-value="updateSettings('defaultModel', $event)"
            />
            
            <!-- Selected model info -->
            <div v-if="selectedModel" class="mt-3 p-3 bg-muted-50 dark:bg-muted-900 rounded-lg">
              <div class="flex items-start justify-between gap-3">
                <div>
                  <BaseText size="sm" weight="medium" class="mb-1">
                    {{ selectedModel.displayName }}
                  </BaseText>
                  <div class="flex flex-wrap gap-3 text-xs text-muted-600 dark:text-muted-400">
                    <span>{{ formatContextWindow(selectedModel.contextWindow) }} context</span>
                    <span v-if="selectedModel.pricing">{{ formatPricing(selectedModel) }}</span>
                    <span v-if="selectedModel.releaseDate">Released {{ selectedModel.releaseDate }}</span>
                  </div>
                </div>
                <BaseBadge
                  v-if="selectedModel.deprecated"
                  color="warning"
                  size="sm"
                >
                  Deprecated
                </BaseBadge>
              </div>
            </div>
          </div>

          <!-- Basic Settings -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Max Tokens -->
            <div>
              <BaseLabel>Max Tokens</BaseLabel>
              <BaseInput
                :model-value="localSettings.maxTokens"
                type="number"
                placeholder="e.g., 4096"
                min="1"
                :max="selectedModel?.contextWindow"
                @update:model-value="updateSettings('maxTokens', $event ? parseInt($event) : undefined)"
              />
              <BaseText size="xs" class="text-muted-500 dark:text-muted-400 mt-1">
                Maximum tokens to generate (leave empty for model default)
              </BaseText>
            </div>

            <!-- Temperature -->
            <div>
              <BaseLabel>Temperature</BaseLabel>
              <BaseInput
                :model-value="localSettings.temperature"
                type="number"
                placeholder="e.g., 0.7"
                min="0"
                max="2"
                step="0.1"
                @update:model-value="updateSettings('temperature', $event ? parseFloat($event) : undefined)"
              />
              <BaseText size="xs" class="text-muted-500 dark:text-muted-400 mt-1">
                Controls randomness (0 = deterministic, 2 = very random)
              </BaseText>
            </div>
          </div>

          <!-- Validation Errors -->
          <div v-if="validationErrors.length > 0" class="space-y-2">
            <BaseAlert
              v-for="error in validationErrors"
              :key="error"
              color="danger"
              size="sm"
            >
              {{ error }}
            </BaseAlert>
          </div>
        </div>
      </BaseTabPanel>

      <!-- Advanced Settings -->
      <BaseTabPanel v-if="showAdvanced" value="advanced">
        <div class="space-y-6">
          <!-- Custom Base URL -->
          <div>
            <BaseLabel>Custom Base URL</BaseLabel>
            <BaseInput
              :model-value="localSettings.baseUrl"
              type="url"
              placeholder="https://api.example.com/v1"
              @update:model-value="updateSettings('baseUrl', $event || undefined)"
            />
            <BaseText size="xs" class="text-muted-500 dark:text-muted-400 mt-1">
              Override the default API endpoint (for proxy or custom deployments)
            </BaseText>
          </div>

          <!-- Reset Settings -->
          <div class="pt-4 border-t border-muted-200 dark:border-muted-800">
            <BaseButton
              color="danger"
              variant="soft"
              @click="resetSettings"
              :disabled="loading"
            >
              <Icon name="ph:arrow-counter-clockwise" class="size-4" />
              Reset to Defaults
            </BaseButton>
          </div>
        </div>
      </BaseTabPanel>

      <!-- Testing Panel -->
      <BaseTabPanel v-if="showTesting" value="testing">
        <div class="space-y-6">
          <!-- Connection Test -->
          <div>
            <div class="flex items-center justify-between mb-4">
              <div>
                <BaseHeading as="h4" size="md" weight="medium">
                  Connection Test
                </BaseHeading>
                <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
                  Test the integration connection and settings
                </BaseText>
              </div>
              
              <BaseButton
                @click="testConnection"
                :loading="testingConnection"
                :disabled="!settingsValid || loading"
              >
                <Icon name="ph:play" class="size-4" />
                Run Test
              </BaseButton>
            </div>

            <!-- Test Results -->
            <div v-if="testResult" class="p-4 rounded-lg border" :class="[
              testResult.success 
                ? 'border-success-200 bg-success-50 dark:border-success-800 dark:bg-success-950/20'
                : 'border-danger-200 bg-danger-50 dark:border-danger-800 dark:bg-danger-950/20'
            ]">
              <div class="flex items-start gap-3">
                <Icon 
                  :name="testResult.success ? 'ph:check-circle' : 'ph:x-circle'"
                  :class="[
                    'size-5 mt-0.5',
                    testResult.success ? 'text-success-600' : 'text-danger-600'
                  ]"
                />
                <div class="flex-1">
                  <BaseText size="sm" weight="medium" :class="[
                    testResult.success ? 'text-success-900 dark:text-success-100' : 'text-danger-900 dark:text-danger-100'
                  ]">
                    {{ testResult.message }}
                  </BaseText>
                  <div v-if="testResult.latency" class="mt-1">
                    <BaseText size="xs" :class="[
                      testResult.success ? 'text-success-700 dark:text-success-300' : 'text-danger-700 dark:text-danger-300'
                    ]">
                      Response time: {{ testResult.latency }}ms
                    </BaseText>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </BaseTabPanel>

      <!-- Analytics Panel -->
      <BaseTabPanel v-if="showAnalytics" value="analytics">
        <div class="space-y-6">
          <!-- Usage Overview -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <BaseCard class="p-4">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
                  <Icon name="ph:chart-bar" class="size-5 text-primary-600 dark:text-primary-400" />
                </div>
                <div>
                  <BaseText size="xs" class="text-muted-600 dark:text-muted-400">Total Requests</BaseText>
                  <BaseText size="lg" weight="bold">{{ usageAnalytics.totalRequests.toLocaleString() }}</BaseText>
                </div>
              </div>
            </BaseCard>

            <BaseCard class="p-4">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-success-100 dark:bg-success-900 rounded-lg">
                  <Icon name="ph:check-circle" class="size-5 text-success-600 dark:text-success-400" />
                </div>
                <div>
                  <BaseText size="xs" class="text-muted-600 dark:text-muted-400">Success Rate</BaseText>
                  <BaseText size="lg" weight="bold">{{ usageAnalytics.successRate }}%</BaseText>
                </div>
              </div>
            </BaseCard>

            <BaseCard class="p-4">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-warning-100 dark:bg-warning-900 rounded-lg">
                  <Icon name="ph:clock" class="size-5 text-warning-600 dark:text-warning-400" />
                </div>
                <div>
                  <BaseText size="xs" class="text-muted-600 dark:text-muted-400">Avg Latency</BaseText>
                  <BaseText size="lg" weight="bold">{{ usageAnalytics.averageLatency }}ms</BaseText>
                </div>
              </div>
            </BaseCard>

            <BaseCard class="p-4">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-info-100 dark:bg-info-900 rounded-lg">
                  <Icon name="ph:currency-dollar" class="size-5 text-info-600 dark:text-info-400" />
                </div>
                <div>
                  <BaseText size="xs" class="text-muted-600 dark:text-muted-400">Est. Cost</BaseText>
                  <BaseText size="lg" weight="bold">${{ usageAnalytics.estimatedCost }}</BaseText>
                </div>
              </div>
            </BaseCard>
          </div>

          <!-- Top Models -->
          <BaseCard class="p-6">
            <BaseHeading as="h4" size="md" weight="medium" class="mb-4">
              Most Used Models
            </BaseHeading>
            <div class="space-y-3">
              <div
                v-for="model in usageAnalytics.topModels"
                :key="model.model"
                class="flex items-center justify-between"
              >
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <BaseText size="sm">{{ model.model }}</BaseText>
                </div>
                <div class="flex items-center gap-3">
                  <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
                    {{ model.requests }} requests
                  </BaseText>
                  <BaseText size="sm" weight="medium">
                    {{ model.percentage }}%
                  </BaseText>
                </div>
              </div>
            </div>
          </BaseCard>
        </div>
      </BaseTabPanel>
    </BaseTabs>
  </div>
</template>
