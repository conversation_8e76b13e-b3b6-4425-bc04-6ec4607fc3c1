<script setup lang="ts">
import type { IntegrationFormData, LLMProvider, LLMModel } from '~/layers/auth-module/types/integration'
import { validateApiKeyFormat } from '~/layers/auth-module/utils/encryption'

interface Props {
  provider?: LLMProvider
  initialData?: Partial<IntegrationFormData>
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<{
  submit: [data: IntegrationFormData]
  cancel: []
}>()

// Form state
const form = reactive<IntegrationFormData>({
  provider: props.provider || 'openai',
  name: '',
  description: '',
  apiKey: '',
  settings: {
    defaultModel: undefined,
    maxTokens: undefined,
    temperature: undefined,
  },
  availableToProfiles: true,
  ...props.initialData,
})

// Validation state
const errors = reactive({
  name: '',
  apiKey: '',
  provider: '',
})

// LLM providers
const {
  providers,
  getModels,
  getModelsWithApiKey,
  getProviderName,
  modelsLoading,
  modelsError,
  formatContextWindow,
  providerSupportsModelFetching
} = useLLMProviders()
const { currentProfile } = useAuth()

// State for enhanced form features
const fetchingModels = ref(false)
const modelFetchError = ref<string | null>(null)
const validatingApiKey = ref(false)
const apiKeyValidationResult = ref<{ isValid: boolean; error?: string; models?: LLMModel[] } | null>(null)
const showAdvancedSettings = ref(false)

// Get models for selected provider (static fallback)
const availableModels = computed(() => getModels(form.provider))

// Dynamic models (fetched with API key)
const dynamicModels = ref<LLMModel[]>([])

// Combined models (dynamic if available, otherwise static)
const displayModels = computed(() => {
  return dynamicModels.value.length > 0 ? dynamicModels.value : availableModels.value
})

// Check if provider supports dynamic fetching
const supportsDynamicFetching = computed(() => {
  return providerSupportsModelFetching(form.provider)
})

// API key validation status
const apiKeyStatus = computed(() => {
  if (validatingApiKey.value) return 'validating'
  if (!form.apiKey) return 'empty'
  if (!validateApiKeyFormat(form.apiKey, form.provider)) return 'invalid-format'
  if (apiKeyValidationResult.value?.isValid) return 'valid'
  if (apiKeyValidationResult.value?.error) return 'invalid'
  return 'unknown'
})

// Form validation status
const isFormValid = computed(() => {
  return form.name.trim() &&
         form.apiKey.trim() &&
         validateApiKeyFormat(form.apiKey, form.provider) &&
         form.provider &&
         !validatingApiKey.value
})

// Show API key as password
const showApiKey = ref(false)

// Validate form
function validateForm() {
  let isValid = true
  errors.name = ''
  errors.apiKey = ''
  errors.provider = ''

  if (!form.name.trim()) {
    errors.name = 'Name is required'
    isValid = false
  }

  if (!form.apiKey.trim()) {
    errors.apiKey = 'API key is required'
    isValid = false
  }
  else if (!validateApiKeyFormat(form.apiKey, form.provider)) {
    errors.apiKey = 'Invalid API key format'
    isValid = false
  }

  if (!form.provider) {
    errors.provider = 'Provider is required'
    isValid = false
  }

  return isValid
}

// Handle submit
function handleSubmit() {
  if (!validateForm())
    return
  emit('submit', { ...form })
}

// Auto-generate name if not provided
watch(() => form.provider, (provider) => {
  if (!form.name && provider) {
    form.name = `${getProviderName(provider)} Integration`
  }
})

// Real-time API key validation with model fetching
const debouncedValidateApiKey = useDebounceFn(async () => {
  if (!form.apiKey || !validateApiKeyFormat(form.apiKey, form.provider)) {
    apiKeyValidationResult.value = null
    dynamicModels.value = []
    return
  }

  try {
    validatingApiKey.value = true
    fetchingModels.value = true
    modelFetchError.value = null

    // Validate API key and fetch models simultaneously
    const [validationResponse, models] = await Promise.allSettled([
      $fetch('/api/integrations/validate', {
        method: 'POST',
        body: {
          provider: form.provider,
          apiKey: form.apiKey,
        },
      }),
      supportsDynamicFetching.value
        ? getModelsWithApiKey(form.provider, form.apiKey, {
            timeout: 8000,
            fallbackToStatic: false,
          })
        : Promise.resolve([])
    ])

    // Handle validation result
    if (validationResponse.status === 'fulfilled') {
      apiKeyValidationResult.value = validationResponse.value
    } else {
      apiKeyValidationResult.value = {
        isValid: false,
        error: 'Validation failed',
      }
    }

    // Handle models result
    if (models.status === 'fulfilled' && models.value.length > 0) {
      dynamicModels.value = models.value

      // Auto-select first model if none selected
      if (!form.settings?.defaultModel) {
        form.settings = {
          ...form.settings,
          defaultModel: models.value[0].id,
        }
      }
    } else {
      dynamicModels.value = []
      if (models.status === 'rejected') {
        modelFetchError.value = 'Failed to fetch models'
      }
    }
  } catch (error) {
    console.warn('API key validation failed:', error)
    apiKeyValidationResult.value = {
      isValid: false,
      error: error instanceof Error ? error.message : 'Validation failed',
    }
    dynamicModels.value = []
  } finally {
    validatingApiKey.value = false
    fetchingModels.value = false
  }
}, 1500) // 1.5 second debounce for validation

// Watch API key for validation and model fetching
watch(() => form.apiKey, () => {
  apiKeyValidationResult.value = null
  debouncedValidateApiKey()
})

// Watch provider changes
watch(() => form.provider, () => {
  dynamicModels.value = []
  modelFetchError.value = null
  apiKeyValidationResult.value = null
  if (form.apiKey) {
    debouncedValidateApiKey()
  }
})

// Handle model selection
const handleModelSelected = (model: LLMModel) => {
  form.settings = {
    ...form.settings,
    defaultModel: model.id,
  }
}

// Test integration connection
const testConnection = async () => {
  if (!form.apiKey || !validateApiKeyFormat(form.apiKey, form.provider)) {
    return
  }

  // Trigger validation
  await debouncedValidateApiKey()
}

// Set first model as default if none selected (for static models)
watch(displayModels, (models) => {
  if (models.length > 0 && !form.settings?.defaultModel) {
    form.settings = {
      ...form.settings,
      defaultModel: models[0].id,
    }
  }
})
</script>

<template>
  <form @submit.prevent="handleSubmit">
    <div class="space-y-5">
      <!-- Provider selection (if not fixed) -->
      <div v-if="!provider">
        <BaseLabel>Provider</BaseLabel>
        <BaseSelect
          v-model="form.provider"
          :error="errors.provider"
          shape="curved"
          icon="ph:robot"
        >
          <option value="">
            Select a provider
          </option>
          <option
            v-for="p in providers"
            :key="p.id"
            :value="p.id"
          >
            {{ p.name }}
          </option>
        </BaseSelect>
      </div>

      <!-- Name -->
      <div>
        <BaseLabel>Integration Name</BaseLabel>
        <BaseInput
          v-model="form.name"
          :error="errors.name"
          shape="curved"
          icon="ph:tag"
          placeholder="e.g., Production OpenAI"
        />
      </div>

      <!-- Description -->
      <div>
        <BaseLabel>Description (Optional)</BaseLabel>
        <BaseTextarea
          v-model="form.description"
          shape="curved"
          placeholder="Brief description of this integration"
          rows="2"
        />
      </div>

      <!-- API Key -->
      <div>
        <div class="flex items-center justify-between mb-2">
          <BaseLabel>API Key</BaseLabel>
          <div class="flex items-center gap-2">
            <!-- Validation status indicator -->
            <div class="flex items-center gap-1">
              <Icon
                v-if="apiKeyStatus === 'validating'"
                name="ph:spinner"
                class="size-4 animate-spin text-primary-500"
              />
              <Icon
                v-else-if="apiKeyStatus === 'valid'"
                name="ph:check-circle"
                class="size-4 text-success-500"
              />
              <Icon
                v-else-if="apiKeyStatus === 'invalid' || apiKeyStatus === 'invalid-format'"
                name="ph:x-circle"
                class="size-4 text-danger-500"
              />
              <BaseText
                v-if="apiKeyStatus !== 'empty' && apiKeyStatus !== 'unknown'"
                size="xs"
                :class="{
                  'text-primary-600': apiKeyStatus === 'validating',
                  'text-success-600': apiKeyStatus === 'valid',
                  'text-danger-600': apiKeyStatus === 'invalid' || apiKeyStatus === 'invalid-format',
                }"
              >
                {{
                  apiKeyStatus === 'validating' ? 'Validating...' :
                  apiKeyStatus === 'valid' ? 'Valid' :
                  apiKeyStatus === 'invalid-format' ? 'Invalid format' :
                  apiKeyStatus === 'invalid' ? 'Invalid key' : ''
                }}
              </BaseText>
            </div>

            <!-- Test connection button -->
            <BaseButton
              v-if="form.apiKey && !validatingApiKey"
              size="sm"
              variant="soft"
              @click="testConnection"
              :disabled="!validateApiKeyFormat(form.apiKey, form.provider)"
            >
              <Icon name="ph:plug" class="size-3" />
              Test
            </BaseButton>
          </div>
        </div>

        <div class="relative">
          <BaseInput
            v-model="form.apiKey"
            :type="showApiKey ? 'text' : 'password'"
            :error="errors.apiKey"
            shape="curved"
            icon="ph:key"
            placeholder="sk-..."
            :class="{
              'border-success-300 dark:border-success-700': apiKeyStatus === 'valid',
              'border-danger-300 dark:border-danger-700': apiKeyStatus === 'invalid' || apiKeyStatus === 'invalid-format',
            }"
          />
          <div class="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
            <!-- Dynamic fetching indicator -->
            <div v-if="supportsDynamicFetching && dynamicModels.length > 0" class="mr-1">
              <Icon name="ph:wifi" class="size-3 text-success-500" />
            </div>

            <!-- Show/hide password toggle -->
            <BaseButtonIcon
              size="sm"
              shape="full"
              @click="showApiKey = !showApiKey"
            >
              <Icon :name="showApiKey ? 'ph:eye-slash' : 'ph:eye'" />
            </BaseButtonIcon>
          </div>
        </div>

        <!-- API key feedback -->
        <div class="mt-1">
          <BaseText
            v-if="apiKeyValidationResult?.error"
            size="xs"
            class="text-danger-600 dark:text-danger-400"
          >
            {{ apiKeyValidationResult.error }}
          </BaseText>
          <BaseText
            v-else-if="apiKeyStatus === 'valid'"
            size="xs"
            class="text-success-600 dark:text-success-400"
          >
            API key validated successfully
            <span v-if="dynamicModels.length > 0"> • {{ dynamicModels.length }} models available</span>
          </BaseText>
          <BaseText
            v-else
            size="xs"
            class="text-muted-500 dark:text-muted-400"
          >
            Your API key will be encrypted before storage
            <span v-if="supportsDynamicFetching"> • Enter a valid key to see latest models</span>
          </BaseText>
        </div>
      </div>

      <!-- Default Model -->
      <div v-if="displayModels.length > 0 || fetchingModels">
        <BaseLabel>Default Model</BaseLabel>

        <!-- Enhanced Model Selector -->
        <TairoModelSelector
          v-model="form.settings.defaultModel"
          :provider="form.provider"
          :api-key="form.apiKey"
          :models="displayModels.length > 0 ? displayModels : undefined"
          :loading="fetchingModels"
          :error="modelFetchError"
          mode="dropdown"
          :show-pricing="true"
          :show-context="true"
          :show-deprecated="true"
          :show-details="false"
          :enable-filtering="displayModels.length > 5"
          :compact="false"
          :disabled="loading"
          @model-selected="handleModelSelected"
        />

        <!-- Model information display -->
        <div v-if="form.settings.defaultModel" class="mt-2">
          <div
            v-for="model in displayModels.filter(m => m.id === form.settings.defaultModel)"
            :key="model.id"
            class="bg-muted-50 dark:bg-muted-900 rounded-lg p-3"
          >
            <div class="flex items-start justify-between gap-3">
              <div class="flex-1">
                <div class="flex items-center gap-2 mb-1">
                  <BaseText size="sm" weight="medium">
                    {{ model.displayName }}
                  </BaseText>
                  <BaseBadge
                    v-if="model.deprecated"
                    color="warning"
                    size="sm"
                  >
                    Deprecated
                  </BaseBadge>
                </div>

                <div class="flex flex-wrap gap-3 text-xs text-muted-600 dark:text-muted-400">
                  <div class="flex items-center gap-1">
                    <Icon name="ph:text-columns" class="size-3" />
                    <span>{{ formatContextWindow(model.contextWindow) }} context</span>
                  </div>

                  <div v-if="model.pricing" class="flex items-center gap-1">
                    <Icon name="ph:currency-dollar" class="size-3" />
                    <span>{{ formatPricing(model) }}</span>
                  </div>

                  <div v-if="model.releaseDate" class="flex items-center gap-1">
                    <Icon name="ph:calendar" class="size-3" />
                    <span>{{ model.releaseDate }}</span>
                  </div>
                </div>

                <!-- Capabilities -->
                <div v-if="model.capabilities.length > 0" class="mt-2 flex flex-wrap gap-1">
                  <BaseBadge
                    v-for="capability in model.capabilities"
                    :key="capability"
                    size="sm"
                    variant="soft"
                  >
                    {{ capability.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) }}
                  </BaseBadge>
                </div>

                <!-- Deprecation warning -->
                <BaseAlert
                  v-if="model.deprecated"
                  color="warning"
                  size="sm"
                  class="mt-2"
                >
                  This model is deprecated and may be removed in the future. Consider selecting a newer model.
                </BaseAlert>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Advanced Settings -->
      <BaseAccordion v-model="showAdvancedSettings">
        <BaseAccordionItem>
          <template #header>
            <div class="flex items-center justify-between w-full">
              <div class="flex items-center gap-2">
                <Icon name="ph:gear" class="size-5" />
                <span>Advanced Settings</span>
              </div>
              <BaseBadge
                v-if="form.settings?.maxTokens || form.settings?.temperature || form.settings?.baseUrl"
                color="primary"
                size="sm"
              >
                Configured
              </BaseBadge>
            </div>
          </template>

          <div class="space-y-4 pt-4">
            <!-- Max Tokens -->
            <div>
              <BaseLabel>Max Tokens (Optional)</BaseLabel>
              <BaseInput
                v-model.number="form.settings.maxTokens"
                type="number"
                shape="curved"
                icon="ph:text-columns"
                placeholder="e.g., 4096"
                min="1"
              />
            </div>

            <!-- Temperature -->
            <div>
              <BaseLabel>Temperature (Optional)</BaseLabel>
              <BaseInput
                v-model.number="form.settings.temperature"
                type="number"
                shape="curved"
                icon="ph:thermometer"
                placeholder="e.g., 0.7"
                min="0"
                max="2"
                step="0.1"
              />
            </div>

            <!-- Custom Base URL -->
            <div>
              <BaseLabel>Custom Base URL (Optional)</BaseLabel>
              <BaseInput
                v-model="form.settings.baseUrl"
                type="url"
                shape="curved"
                icon="ph:link"
                placeholder="https://api.example.com/v1"
              />
            </div>
          </div>
        </BaseAccordionItem>
      </BaseAccordion>

      <!-- Profile availability (only for user-level integrations) -->
      <div v-if="!currentProfile">
        <BaseCheckbox
          v-model="form.availableToProfiles"
          shape="curved"
        >
          Make available to all my profiles
        </BaseCheckbox>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end gap-3 pt-4">
        <BaseButton
          variant="soft"
          @click="emit('cancel')"
        >
          Cancel
        </BaseButton>
        <BaseButton
          type="submit"
          color="primary"
          :loading="loading"
          :disabled="!isFormValid"
        >
          <Icon name="ph:check" class="size-4" />
          <span>Save Integration</span>
        </BaseButton>
      </div>
    </div>
  </form>
</template>
