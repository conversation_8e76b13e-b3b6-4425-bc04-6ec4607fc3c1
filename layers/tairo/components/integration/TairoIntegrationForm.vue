<script setup lang="ts">
import type { IntegrationFormData, LLMProvider, LLMModel } from '~/layers/auth-module/types/integration'
import { validateApiKeyFormat } from '~/layers/auth-module/utils/encryption'

interface Props {
  provider?: LLMProvider
  initialData?: Partial<IntegrationFormData>
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<{
  submit: [data: IntegrationFormData]
  cancel: []
}>()

// Form state
const form = reactive<IntegrationFormData>({
  provider: props.provider || 'openai',
  name: '',
  description: '',
  apiKey: '',
  settings: {
    defaultModel: undefined,
    maxTokens: undefined,
    temperature: undefined,
  },
  availableToProfiles: true,
  ...props.initialData,
})

// Validation state
const errors = reactive({
  name: '',
  apiKey: '',
  provider: '',
})

// LLM providers
const {
  providers,
  getModels,
  getModelsWithApiKey,
  getProviderName,
  modelsLoading,
  modelsError,
  formatContextWindow,
  providerSupportsModelFetching
} = useLLMProviders()
const { currentProfile } = useAuth()

// State for dynamic model fetching
const fetchingModels = ref(false)
const modelFetchError = ref<string | null>(null)

// Get models for selected provider (static fallback)
const availableModels = computed(() => getModels(form.provider))

// Dynamic models (fetched with API key)
const dynamicModels = ref<LLMModel[]>([])

// Combined models (dynamic if available, otherwise static)
const displayModels = computed(() => {
  return dynamicModels.value.length > 0 ? dynamicModels.value : availableModels.value
})

// Check if provider supports dynamic fetching
const supportsDynamicFetching = computed(() => {
  return providerSupportsModelFetching(form.provider)
})

// Show API key as password
const showApiKey = ref(false)

// Validate form
function validateForm() {
  let isValid = true
  errors.name = ''
  errors.apiKey = ''
  errors.provider = ''

  if (!form.name.trim()) {
    errors.name = 'Name is required'
    isValid = false
  }

  if (!form.apiKey.trim()) {
    errors.apiKey = 'API key is required'
    isValid = false
  }
  else if (!validateApiKeyFormat(form.apiKey, form.provider)) {
    errors.apiKey = 'Invalid API key format'
    isValid = false
  }

  if (!form.provider) {
    errors.provider = 'Provider is required'
    isValid = false
  }

  return isValid
}

// Handle submit
function handleSubmit() {
  if (!validateForm())
    return
  emit('submit', { ...form })
}

// Auto-generate name if not provided
watch(() => form.provider, (provider) => {
  if (!form.name && provider) {
    form.name = `${getProviderName(provider)} Integration`
  }
})

// Fetch models when API key changes
const debouncedFetchModels = useDebounceFn(async () => {
  if (!form.apiKey || !supportsDynamicFetching.value) {
    dynamicModels.value = []
    return
  }

  if (!validateApiKeyFormat(form.apiKey, form.provider)) {
    dynamicModels.value = []
    return
  }

  try {
    fetchingModels.value = true
    modelFetchError.value = null

    const models = await getModelsWithApiKey(form.provider, form.apiKey, {
      timeout: 8000, // 8 second timeout for form
      fallbackToStatic: false, // Don't fallback in form, we handle it manually
    })

    dynamicModels.value = models

    // Auto-select first model if none selected
    if (models.length > 0 && !form.settings?.defaultModel) {
      form.settings = {
        ...form.settings,
        defaultModel: models[0].id,
      }
    }
  } catch (error) {
    console.warn('Failed to fetch models:', error)
    modelFetchError.value = error instanceof Error ? error.message : 'Failed to fetch models'
    dynamicModels.value = []
  } finally {
    fetchingModels.value = false
  }
}, 1000) // 1 second debounce

// Watch API key for dynamic model fetching
watch(() => form.apiKey, () => {
  debouncedFetchModels()
})

// Watch provider changes
watch(() => form.provider, () => {
  dynamicModels.value = []
  modelFetchError.value = null
  if (form.apiKey) {
    debouncedFetchModels()
  }
})

// Set first model as default if none selected (for static models)
watch(displayModels, (models) => {
  if (models.length > 0 && !form.settings?.defaultModel) {
    form.settings = {
      ...form.settings,
      defaultModel: models[0].id,
    }
  }
})
</script>

<template>
  <form @submit.prevent="handleSubmit">
    <div class="space-y-5">
      <!-- Provider selection (if not fixed) -->
      <div v-if="!provider">
        <BaseLabel>Provider</BaseLabel>
        <BaseSelect
          v-model="form.provider"
          :error="errors.provider"
          shape="curved"
          icon="ph:robot"
        >
          <option value="">
            Select a provider
          </option>
          <option
            v-for="p in providers"
            :key="p.id"
            :value="p.id"
          >
            {{ p.name }}
          </option>
        </BaseSelect>
      </div>

      <!-- Name -->
      <div>
        <BaseLabel>Integration Name</BaseLabel>
        <BaseInput
          v-model="form.name"
          :error="errors.name"
          shape="curved"
          icon="ph:tag"
          placeholder="e.g., Production OpenAI"
        />
      </div>

      <!-- Description -->
      <div>
        <BaseLabel>Description (Optional)</BaseLabel>
        <BaseTextarea
          v-model="form.description"
          shape="curved"
          placeholder="Brief description of this integration"
          rows="2"
        />
      </div>

      <!-- API Key -->
      <div>
        <BaseLabel>API Key</BaseLabel>
        <div class="relative">
          <BaseInput
            v-model="form.apiKey"
            :type="showApiKey ? 'text' : 'password'"
            :error="errors.apiKey"
            shape="curved"
            icon="ph:key"
            placeholder="sk-..."
          />
          <div class="absolute right-3 top-1/2 -translate-y-1/2">
            <BaseButtonIcon
              size="sm"
              shape="full"
              @click="showApiKey = !showApiKey"
            >
              <Icon :name="showApiKey ? 'ph:eye-slash' : 'ph:eye'" />
            </BaseButtonIcon>
          </div>
        </div>
        <BaseText
          size="xs"
          class="text-muted-500 dark:text-muted-400 mt-1"
        >
          Your API key will be encrypted before storage
        </BaseText>
      </div>

      <!-- Default Model -->
      <div v-if="displayModels.length > 0">
        <div class="flex items-center justify-between">
          <BaseLabel>Default Model</BaseLabel>
          <div class="flex items-center gap-2">
            <!-- Dynamic fetching indicator -->
            <div v-if="supportsDynamicFetching && form.apiKey" class="flex items-center gap-1">
              <Icon
                v-if="fetchingModels"
                name="ph:spinner"
                class="size-4 animate-spin text-primary-500"
              />
              <Icon
                v-else-if="dynamicModels.length > 0"
                name="ph:check-circle"
                class="size-4 text-success-500"
              />
              <Icon
                v-else-if="modelFetchError"
                name="ph:warning-circle"
                class="size-4 text-warning-500"
              />
              <BaseText
                size="xs"
                class="text-muted-500 dark:text-muted-400"
              >
                {{ fetchingModels ? 'Fetching...' : dynamicModels.length > 0 ? 'Live models' : 'Static models' }}
              </BaseText>
            </div>
          </div>
        </div>

        <BaseSelect
          v-model="form.settings.defaultModel"
          shape="curved"
          icon="ph:cpu"
          :loading="fetchingModels"
        >
          <option
            v-for="model in displayModels"
            :key="model.id"
            :value="model.id"
          >
            {{ model.displayName }}
            <template v-if="model.contextWindow">
              ({{ formatContextWindow(model.contextWindow) }})
            </template>
            <template v-if="model.deprecated">
              (Deprecated)
            </template>
          </option>
        </BaseSelect>

        <!-- Model fetch error -->
        <BaseText
          v-if="modelFetchError"
          size="xs"
          class="text-warning-600 dark:text-warning-400 mt-1"
        >
          {{ modelFetchError }} - Using static models
        </BaseText>

        <!-- Dynamic fetching help text -->
        <BaseText
          v-else-if="supportsDynamicFetching && !form.apiKey"
          size="xs"
          class="text-muted-500 dark:text-muted-400 mt-1"
        >
          Enter a valid API key to see the latest available models
        </BaseText>
      </div>

      <!-- Advanced Settings -->
      <BaseAccordion>
        <BaseAccordionItem>
          <template #header>
            <div class="flex items-center gap-2">
              <Icon name="ph:gear" class="size-5" />
              <span>Advanced Settings</span>
            </div>
          </template>

          <div class="space-y-4 pt-4">
            <!-- Max Tokens -->
            <div>
              <BaseLabel>Max Tokens (Optional)</BaseLabel>
              <BaseInput
                v-model.number="form.settings.maxTokens"
                type="number"
                shape="curved"
                icon="ph:text-columns"
                placeholder="e.g., 4096"
                min="1"
              />
            </div>

            <!-- Temperature -->
            <div>
              <BaseLabel>Temperature (Optional)</BaseLabel>
              <BaseInput
                v-model.number="form.settings.temperature"
                type="number"
                shape="curved"
                icon="ph:thermometer"
                placeholder="e.g., 0.7"
                min="0"
                max="2"
                step="0.1"
              />
            </div>

            <!-- Custom Base URL -->
            <div>
              <BaseLabel>Custom Base URL (Optional)</BaseLabel>
              <BaseInput
                v-model="form.settings.baseUrl"
                type="url"
                shape="curved"
                icon="ph:link"
                placeholder="https://api.example.com/v1"
              />
            </div>
          </div>
        </BaseAccordionItem>
      </BaseAccordion>

      <!-- Profile availability (only for user-level integrations) -->
      <div v-if="!currentProfile">
        <BaseCheckbox
          v-model="form.availableToProfiles"
          shape="curved"
        >
          Make available to all my profiles
        </BaseCheckbox>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end gap-3 pt-4">
        <BaseButton
          variant="soft"
          @click="emit('cancel')"
        >
          Cancel
        </BaseButton>
        <BaseButton
          type="submit"
          color="primary"
          :loading="loading"
        >
          <Icon name="ph:check" class="size-4" />
          <span>Save Integration</span>
        </BaseButton>
      </div>
    </div>
  </form>
</template>
