<script setup lang="ts">
import type { Integration } from '~/layers/auth-module/types/integration'

interface Props {
  integration: Integration
  showInheritance?: boolean
  inheritanceStatus?: {
    isProfileSpecific: boolean
    isInherited: boolean
    isOverridden: boolean
    isEffective: boolean
    source: 'profile' | 'user'
  }
  /** Show detailed model information */
  showModelDetails?: boolean
  /** Show usage analytics */
  showUsageStats?: boolean
  /** Show quick actions */
  showQuickActions?: boolean
  /** Compact display mode */
  compact?: boolean
  /** Loading state */
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showInheritance: false,
  showModelDetails: true,
  showUsageStats: true,
  showQuickActions: true,
  compact: false,
  loading: false,
})

const emit = defineEmits<{
  toggle: [value: boolean]
  edit: []
  delete: []
  setDefault: []
  test: []
  duplicate: []
  viewUsage: []
}>()

// Get provider config
const {
  getProvider,
  getProviderIcon,
  formatContextWindow,
  formatPricing,
  getModels
} = useLLMProviders()

const provider = computed(() => getProvider(props.integration.provider))

// Get default model info
const defaultModel = computed(() => {
  if (!provider.value || !props.integration.settings.defaultModel)
    return null

  // Try to get from provider models first
  const providerModels = getModels(props.integration.provider)
  const model = providerModels.find(m => m.id === props.integration.settings.defaultModel)

  if (model) return model

  // Fallback to static models
  return provider.value.models.find(m => m.id === props.integration.settings.defaultModel)
})

// Connection status
const connectionStatus = computed(() => {
  if (props.loading) return 'checking'
  if (!props.integration.isActive) return 'inactive'
  if (props.integration.lastUsedAt) {
    const daysSinceLastUse = (Date.now() - props.integration.lastUsedAt.toMillis()) / (1000 * 60 * 60 * 24)
    if (daysSinceLastUse > 30) return 'stale'
    if (daysSinceLastUse > 7) return 'idle'
  }
  return 'active'
})

// Status color and icon
const statusConfig = computed(() => {
  switch (connectionStatus.value) {
    case 'checking':
      return { color: 'primary', icon: 'ph:spinner', text: 'Checking...' }
    case 'active':
      return { color: 'success', icon: 'ph:check-circle', text: 'Active' }
    case 'idle':
      return { color: 'warning', icon: 'ph:clock', text: 'Idle' }
    case 'stale':
      return { color: 'muted', icon: 'ph:warning', text: 'Stale' }
    case 'inactive':
      return { color: 'muted', icon: 'ph:x-circle', text: 'Inactive' }
    default:
      return { color: 'muted', icon: 'ph:question', text: 'Unknown' }
  }
})

// Format last used date
const lastUsedText = computed(() => {
  if (!props.integration.lastUsedAt) return 'Never used'
  return useTimeAgo(props.integration.lastUsedAt.toDate()).value
})

// Handle actions
function handleToggle(value: boolean) {
  emit('toggle', value)
}

function handleTest() {
  emit('test')
}

function handleDuplicate() {
  emit('duplicate')
}

function handleViewUsage() {
  emit('viewUsage')
}
</script>

<template>
  <div class="group relative">
    <!-- Inheritance indicator -->
    <div
      v-if="showInheritance && inheritanceStatus?.isInherited"
      class="absolute -top-2 -left-2 z-10"
    >
      <BaseTooltip
        :content="inheritanceStatus.isOverridden ? 'Inherited from user (overridden)' : 'Inherited from user'"
      >
        <div class="bg-primary-500/10 text-primary-600 dark:bg-primary-500/20 dark:text-primary-400 rounded-full p-1">
          <Icon
            :name="inheritanceStatus.isOverridden ? 'ph:link-break' : 'ph:link'"
            class="size-3"
          />
        </div>
      </BaseTooltip>
    </div>

    <div
      class="relative rounded-xl border transition-all duration-200 group-hover:shadow-lg" :class="[
        integration.isActive
          ? 'border-success-500/20 bg-success-50/50 dark:bg-success-950/20'
          : 'border-muted-200 bg-white dark:border-muted-800 dark:bg-muted-950',
        inheritanceStatus?.isOverridden ? 'opacity-60' : '',
        loading ? 'animate-pulse' : '',
      ]"
    >
      <div class="p-5">
        <div class="flex items-start justify-between gap-4">
          <!-- Provider info -->
          <div class="flex items-start gap-4 flex-1">
            <div class="shrink-0">
              <div
                class="flex size-12 items-center justify-center rounded-xl border" :class="[
                  integration.isActive
                    ? 'border-success-500/20 bg-success-500/10'
                    : 'border-muted-200 bg-muted-50 dark:border-muted-800 dark:bg-muted-900',
                ]"
              >
                <Icon
                  :name="getProviderIcon(integration.provider)"
                  class="size-6"
                  :class="[
                    integration.isActive
                      ? 'text-success-600 dark:text-success-400'
                      : 'text-muted-600 dark:text-muted-400',
                  ]"
                />
              </div>
            </div>

            <div class="min-w-0 flex-1">
              <div class="flex items-center gap-2 mb-1">
                <BaseHeading
                  as="h4"
                  size="sm"
                  weight="medium"
                  class="text-muted-900 dark:text-white"
                >
                  {{ integration.name }}
                </BaseHeading>

                <!-- Status badges -->
                <div class="flex items-center gap-1">
                  <BaseTag
                    v-if="integration.isDefault"
                    size="sm"
                    variant="primary"
                    shape="full"
                  >
                    Default
                  </BaseTag>

                  <!-- Connection status -->
                  <BaseTooltip :content="statusConfig.text">
                    <div class="flex items-center gap-1">
                      <Icon
                        :name="statusConfig.icon"
                        :class="[
                          'size-3',
                          connectionStatus === 'checking' ? 'animate-spin' : '',
                          statusConfig.color === 'success' ? 'text-success-500' :
                          statusConfig.color === 'warning' ? 'text-warning-500' :
                          statusConfig.color === 'primary' ? 'text-primary-500' :
                          'text-muted-400'
                        ]"
                      />
                    </div>
                  </BaseTooltip>

                  <!-- Inheritance indicator -->
                  <BaseTooltip
                    v-if="showInheritance && inheritanceStatus?.isInherited"
                    :content="inheritanceStatus.isOverridden ? 'Inherited (overridden)' : 'Inherited from user'"
                  >
                    <Icon
                      :name="inheritanceStatus.isOverridden ? 'ph:link-break' : 'ph:link'"
                      class="size-3 text-primary-500"
                    />
                  </BaseTooltip>

                  <!-- Profile availability indicator -->
                  <BaseTooltip
                    v-if="integration.availableToProfiles"
                    content="Available to profiles"
                  >
                    <Icon name="ph:users" class="size-3 text-muted-400" />
                  </BaseTooltip>
                </div>
              </div>

              <BaseParagraph
                v-if="integration.description && !compact"
                size="xs"
                class="text-muted-500 dark:text-muted-400 mb-2"
              >
                {{ integration.description }}
              </BaseParagraph>

              <!-- Model and usage information -->
              <div v-if="showModelDetails && !compact" class="space-y-2">
                <!-- Model information -->
                <div class="flex flex-wrap items-center gap-4 text-xs">
                  <div class="flex items-center gap-1.5">
                    <Icon
                      name="ph:robot"
                      class="size-4 text-muted-400"
                    />
                    <span class="text-muted-600 dark:text-muted-400">
                      {{ defaultModel?.displayName || 'No model selected' }}
                    </span>
                    <BaseBadge
                      v-if="defaultModel?.deprecated"
                      color="warning"
                      size="sm"
                    >
                      Deprecated
                    </BaseBadge>
                  </div>

                  <div
                    v-if="defaultModel"
                    class="flex items-center gap-1.5"
                  >
                    <Icon
                      name="ph:text-columns"
                      class="size-4 text-muted-400"
                    />
                    <span class="text-muted-600 dark:text-muted-400">
                      {{ formatContextWindow(defaultModel.contextWindow) }} context
                    </span>
                  </div>

                  <div
                    v-if="defaultModel?.pricing"
                    class="flex items-center gap-1.5"
                  >
                    <Icon
                      name="ph:currency-dollar"
                      class="size-4 text-muted-400"
                    />
                    <span class="text-muted-600 dark:text-muted-400">
                      {{ formatPricing(defaultModel) }}
                    </span>
                  </div>
                </div>

                <!-- Usage information -->
                <div v-if="showUsageStats" class="flex flex-wrap items-center gap-4 text-xs">
                  <div class="flex items-center gap-1.5">
                    <Icon
                      name="ph:clock"
                      class="size-4 text-muted-400"
                    />
                    <span class="text-muted-600 dark:text-muted-400">
                      Last used {{ lastUsedText }}
                    </span>
                  </div>

                  <div class="flex items-center gap-1.5">
                    <Icon
                      name="ph:calendar"
                      class="size-4 text-muted-400"
                    />
                    <span class="text-muted-600 dark:text-muted-400">
                      Created {{ useTimeAgo(integration.createdAt.toDate()).value }}
                    </span>
                  </div>
                </div>

                <!-- Model capabilities -->
                <div v-if="defaultModel?.capabilities.length" class="flex flex-wrap gap-1">
                  <BaseBadge
                    v-for="capability in defaultModel.capabilities.slice(0, 3)"
                    :key="capability"
                    size="sm"
                    variant="soft"
                  >
                    {{ capability.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) }}
                  </BaseBadge>
                  <BaseBadge
                    v-if="defaultModel.capabilities.length > 3"
                    size="sm"
                    variant="soft"
                    color="muted"
                  >
                    +{{ defaultModel.capabilities.length - 3 }} more
                  </BaseBadge>
                </div>
              </div>

              <!-- Compact mode info -->
              <div v-else-if="compact" class="flex items-center gap-3 text-xs text-muted-600 dark:text-muted-400">
                <span>{{ defaultModel?.displayName || 'No model' }}</span>
                <span>{{ lastUsedText }}</span>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center gap-2">
            <!-- Quick actions dropdown -->
            <BaseDropdown v-if="showQuickActions" placement="bottom-end">
              <BaseButtonIcon
                size="sm"
                shape="full"
                data-tooltip="More actions"
              >
                <Icon name="ph:dots-three" />
              </BaseButtonIcon>

              <template #content>
                <BaseDropdownItem @click="handleTest">
                  <Icon name="ph:plug" />
                  <span>Test Connection</span>
                </BaseDropdownItem>

                <BaseDropdownItem @click="handleDuplicate">
                  <Icon name="ph:copy" />
                  <span>Duplicate</span>
                </BaseDropdownItem>

                <BaseDropdownItem v-if="showUsageStats" @click="handleViewUsage">
                  <Icon name="ph:chart-line" />
                  <span>View Usage</span>
                </BaseDropdownItem>

                <BaseDropdownDivider />

                <BaseDropdownItem @click="emit('edit')">
                  <Icon name="ph:pencil" />
                  <span>Edit</span>
                </BaseDropdownItem>

                <BaseDropdownItem
                  v-if="integration.isActive && !integration.isDefault"
                  @click="emit('setDefault')"
                >
                  <Icon name="ph:star" />
                  <span>Set as Default</span>
                </BaseDropdownItem>

                <BaseDropdownDivider />

                <BaseDropdownItem color="danger" @click="emit('delete')">
                  <Icon name="ph:trash" />
                  <span>Delete</span>
                </BaseDropdownItem>
              </template>
            </BaseDropdown>

            <!-- Direct action buttons (when not using dropdown) -->
            <template v-else>
              <!-- Test button -->
              <BaseButtonIcon
                v-if="integration.isActive"
                size="sm"
                shape="full"
                variant="soft"
                data-tooltip="Test connection"
                @click="handleTest"
              >
                <Icon name="ph:plug" />
              </BaseButtonIcon>

              <!-- Edit button -->
              <BaseButtonIcon
                size="sm"
                shape="full"
                data-tooltip="Edit integration"
                @click="emit('edit')"
              >
                <Icon name="ph:pencil" />
              </BaseButtonIcon>

              <!-- Delete button -->
              <BaseButtonIcon
                size="sm"
                shape="full"
                color="danger"
                data-tooltip="Delete integration"
                @click="emit('delete')"
              >
                <Icon name="ph:trash" />
              </BaseButtonIcon>
            </template>

            <!-- Toggle switch -->
            <BaseSwitchBall
              :model-value="integration.isActive"
              :disabled="loading"
              @update:model-value="handleToggle"
            />
          </div>
        </div>

        <!-- Deprecation warning -->
        <div
          v-if="defaultModel?.deprecated && integration.isActive"
          class="mt-4 pt-4 border-t border-muted-200 dark:border-muted-800"
        >
          <BaseAlert color="warning" size="sm">
            <template #icon>
              <Icon name="ph:warning" />
            </template>
            The selected model "{{ defaultModel.displayName }}" is deprecated.
            Consider updating to a newer model.
          </BaseAlert>
        </div>

        <!-- Quick set as default action -->
        <div
          v-else-if="integration.isActive && !integration.isDefault && !showQuickActions"
          class="mt-4 pt-4 border-t border-muted-200 dark:border-muted-800"
        >
          <BaseButton
            size="sm"
            variant="soft"
            @click="emit('setDefault')"
          >
            <Icon name="ph:star" class="size-4" />
            <span>Set as default</span>
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>
