<script setup lang="ts">
import type { LLMModel, LLMProvider } from '~/layers/auth-module/types/integration'

/**
 * Enhanced model selector component with advanced features
 * Supports both dropdown and expanded list views
 */

interface Props {
  /** LLM provider */
  provider: LLMProvider
  /** Selected model ID */
  modelValue?: string
  /** API key for dynamic model fetching */
  apiKey?: string
  /** Available models (if provided, skips fetching) */
  models?: LLMModel[]
  /** Display mode: dropdown or list */
  mode?: 'dropdown' | 'list'
  /** Show pricing information */
  showPricing?: boolean
  /** Show context window */
  showContext?: boolean
  /** Show deprecation warnings */
  showDeprecated?: boolean
  /** Show detailed model information */
  showDetails?: boolean
  /** Enable search and filtering */
  enableFiltering?: boolean
  /** Enable model comparison */
  enableComparison?: boolean
  /** Compact display mode */
  compact?: boolean
  /** Loading state */
  loading?: boolean
  /** Error message */
  error?: string
  /** Disabled state */
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'dropdown',
  showPricing: true,
  showContext: true,
  showDeprecated: true,
  showDetails: false,
  enableFiltering: false,
  enableComparison: false,
  compact: false,
  loading: false,
  disabled: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: string | undefined]
  'model-selected': [model: LLMModel]
  'compare-models': [models: LLMModel[]]
  'fetch-models': []
}>()

// Composables
const {
  getModels,
  getModelsWithApiKey,
  getStaticModels,
  formatContextWindow,
  formatPricing,
  getActiveModels,
  modelsLoading,
  modelsError,
  providerSupportsModelFetching
} = useLLMProviders()

// State
const internalModels = ref<LLMModel[]>([])
const fetchingModels = ref(false)
const fetchError = ref<string | null>(null)
const searchQuery = ref('')
const selectedCapability = ref<string>('')
const sortBy = ref<'name' | 'context' | 'price' | 'date'>('name')
const comparedModels = ref<Set<string>>(new Set())

// Computed properties
const availableModels = computed(() => {
  if (props.models) return props.models
  if (internalModels.value.length > 0) return internalModels.value
  return getActiveModels(props.provider)
})

const capabilities = computed(() => {
  const caps = new Set<string>()
  availableModels.value.forEach(model => {
    model.capabilities.forEach(cap => caps.add(cap))
  })
  return Array.from(caps).sort()
})

const filteredModels = computed(() => {
  let filtered = availableModels.value

  // Filter by search query
  if (searchQuery.value && props.enableFiltering) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(model =>
      model.id.toLowerCase().includes(query) ||
      model.name.toLowerCase().includes(query) ||
      model.displayName.toLowerCase().includes(query)
    )
  }

  // Filter by capability
  if (selectedCapability.value && props.enableFiltering) {
    filtered = filtered.filter(model =>
      model.capabilities.includes(selectedCapability.value)
    )
  }

  // Filter deprecated models if not showing them
  if (!props.showDeprecated) {
    filtered = filtered.filter(model => !model.deprecated)
  }

  return filtered
})

const sortedModels = computed(() => {
  const sorted = [...filteredModels.value]

  sorted.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.displayName.localeCompare(b.displayName)
      case 'context':
        return b.contextWindow - a.contextWindow
      case 'price':
        const priceA = a.pricing?.input || Infinity
        const priceB = b.pricing?.input || Infinity
        return priceA - priceB
      case 'date':
        const dateA = a.releaseDate || '0000-00-00'
        const dateB = b.releaseDate || '0000-00-00'
        return dateB.localeCompare(dateA)
      default:
        return 0
    }
  })

  return sorted
})

const selectedModel = computed(() => {
  return availableModels.value.find(model => model.id === props.modelValue)
})

const isLoading = computed(() => {
  return props.loading || fetchingModels.value || modelsLoading.value[props.provider]
})

const displayError = computed(() => {
  return props.error || fetchError.value || modelsError.value[props.provider]
})

const supportsDynamicFetching = computed(() => {
  return providerSupportsModelFetching(props.provider)
})

// Methods
const fetchModels = async () => {
  if (!props.apiKey || props.models) return

  try {
    fetchingModels.value = true
    fetchError.value = null

    const models = await getModelsWithApiKey(props.provider, props.apiKey, {
      timeout: 10000,
      fallbackToStatic: true,
    })

    internalModels.value = models
    emit('fetch-models')
  } catch (error) {
    console.warn('Failed to fetch models:', error)
    fetchError.value = error instanceof Error ? error.message : 'Failed to fetch models'
    // Fallback to static models
    internalModels.value = getStaticModels(props.provider)
  } finally {
    fetchingModels.value = false
  }
}

const selectModel = (model: LLMModel) => {
  if (props.disabled) return

  emit('update:modelValue', model.id)
  emit('model-selected', model)
}

const handleSelect = (modelId: string) => {
  const model = availableModels.value.find(m => m.id === modelId)
  if (model) {
    selectModel(model)
  }
}

const toggleComparison = (model: LLMModel) => {
  if (comparedModels.value.has(model.id)) {
    comparedModels.value.delete(model.id)
  } else {
    comparedModels.value.add(model.id)
  }

  const models = availableModels.value.filter(m => comparedModels.value.has(m.id))
  emit('compare-models', models)
}

const formatCapability = (capability: string) => {
  return capability.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

// Watchers
watch(() => props.apiKey, () => {
  if (props.apiKey && !props.models && supportsDynamicFetching.value) {
    fetchModels()
  }
}, { immediate: true })

watch(() => props.provider, () => {
  internalModels.value = []
  fetchError.value = null
  comparedModels.value.clear()
  if (props.apiKey && !props.models && supportsDynamicFetching.value) {
    fetchModels()
  }
})

// Initialize with static models if no API key
onMounted(() => {
  if (!props.models && !props.apiKey) {
    internalModels.value = getStaticModels(props.provider)
  }
})
</script>

<template>
  <div>
    <!-- Dropdown Mode -->
    <div v-if="mode === 'dropdown'">
      <BaseListbox
        :model-value="modelValue"
        :disabled="disabled || isLoading"
        @update:model-value="handleSelect"
      >
        <BaseListboxButton shape="curved">
          <div class="flex items-center justify-between w-full">
            <div class="flex items-center gap-2">
              <!-- Loading indicator -->
              <Icon
                v-if="isLoading"
                name="ph:spinner"
                class="size-4 animate-spin text-primary-500"
              />

              <!-- Selected model -->
              <span v-if="selectedModel && !isLoading">
                {{ selectedModel.displayName }}
                <span v-if="selectedModel.deprecated" class="text-warning-500 ml-1">(Deprecated)</span>
              </span>

              <!-- Placeholder -->
              <span v-else-if="!isLoading" class="text-muted-400">
                Select a model
              </span>

              <!-- Loading text -->
              <span v-else class="text-muted-400">
                Loading models...
              </span>
            </div>

            <!-- Dynamic indicator -->
            <div v-if="supportsDynamicFetching && apiKey" class="flex items-center gap-1">
              <Icon
                name="ph:wifi"
                class="size-3"
                :class="internalModels.length > 0 ? 'text-success-500' : 'text-muted-400'"
              />
            </div>
          </div>
        </BaseListboxButton>

        <BaseListboxOptions>
          <!-- Error state -->
          <div v-if="displayError" class="p-3">
            <BaseAlert color="warning" size="sm">
              {{ displayError }}
            </BaseAlert>
          </div>

          <!-- Filtering controls -->
          <div v-if="enableFiltering && sortedModels.length > 5" class="p-3 border-b border-muted-200 dark:border-muted-800">
            <BaseInput
              v-model="searchQuery"
              icon="ph:magnifying-glass"
              placeholder="Search models..."
              size="sm"
              shape="curved"
            />

            <div v-if="capabilities.length > 1" class="mt-2">
              <BaseSelect
                v-model="selectedCapability"
                size="sm"
                shape="curved"
              >
                <option value="">All Capabilities</option>
                <option
                  v-for="capability in capabilities"
                  :key="capability"
                  :value="capability"
                >
                  {{ formatCapability(capability) }}
                </option>
              </BaseSelect>
            </div>
          </div>

          <!-- Models list -->
          <BaseListboxOption
            v-for="model in sortedModels"
            :key="model.id"
            :value="model.id"
          >
            <div class="flex items-start justify-between gap-3">
              <div class="min-w-0 flex-1">
                <div class="flex items-center gap-2">
                  <BaseText
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-200"
                  >
                    {{ model.displayName }}
                  </BaseText>

                  <!-- Status badges -->
                  <BaseBadge
                    v-if="model.deprecated"
                    color="warning"
                    size="sm"
                  >
                    Deprecated
                  </BaseBadge>
                </div>

                <div v-if="!compact" class="mt-1 flex flex-wrap items-center gap-3">
                  <!-- Context window -->
                  <div
                    v-if="showContext && model.contextWindow"
                    class="flex items-center gap-1"
                  >
                    <Icon
                      name="ph:text-columns"
                      class="size-3 text-muted-400"
                    />
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      {{ formatContextWindow(model.contextWindow) }}
                    </BaseText>
                  </div>

                  <!-- Pricing -->
                  <div
                    v-if="showPricing && model.pricing"
                    class="flex items-center gap-1"
                  >
                    <Icon
                      name="ph:currency-dollar"
                      class="size-3 text-muted-400"
                    />
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      {{ formatPricing(model) }}
                    </BaseText>
                  </div>

                  <!-- Release date -->
                  <div
                    v-if="model.releaseDate"
                    class="flex items-center gap-1"
                  >
                    <Icon
                      name="ph:calendar"
                      class="size-3 text-muted-400"
                    />
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      {{ model.releaseDate }}
                    </BaseText>
                  </div>

                  <!-- Knowledge cutoff -->
                  <div
                    v-if="model.knowledgeCutoff"
                    class="flex items-center gap-1"
                  >
                    <Icon
                      name="ph:brain"
                      class="size-3 text-muted-400"
                    />
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      {{ model.knowledgeCutoff }}
                    </BaseText>
                  </div>
                </div>

                <!-- Capabilities -->
                <div
                  v-if="!compact && model.capabilities.length > 0"
                  class="mt-2 flex flex-wrap gap-1"
                >
                  <BaseTag
                    v-for="capability in model.capabilities"
                    :key="capability"
                    size="xs"
                    variant="soft"
                    shape="full"
                  >
                    {{ formatCapability(capability) }}
                  </BaseTag>
                </div>
              </div>

              <div class="flex items-center gap-2">
                <!-- Comparison checkbox -->
                <BaseCheckbox
                  v-if="enableComparison"
                  :model-value="comparedModels.has(model.id)"
                  @update:model-value="toggleComparison(model)"
                  @click.stop
                  size="sm"
                />

                <!-- Selected indicator -->
                <Icon
                  v-if="model.id === modelValue"
                  name="ph:check-circle-fill"
                  class="size-5 text-primary-500 shrink-0"
                />
              </div>
            </div>
          </BaseListboxOption>

          <!-- Empty state -->
          <div v-if="sortedModels.length === 0 && !isLoading" class="p-6 text-center">
            <Icon name="ph:robot" class="size-8 mx-auto mb-2 text-muted-400" />
            <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
              {{ searchQuery ? 'No models match your search.' : 'No models available.' }}
            </BaseText>
          </div>
        </BaseListboxOptions>
      </BaseListbox>
    </div>

    <!-- List Mode -->
    <div v-else-if="mode === 'list'" class="space-y-4">
      <!-- Search and Filters -->
      <div v-if="enableFiltering" class="flex flex-col sm:flex-row gap-3">
        <!-- Search -->
        <div class="flex-1">
          <BaseInput
            v-model="searchQuery"
            icon="ph:magnifying-glass"
            placeholder="Search models..."
            shape="curved"
            :disabled="disabled"
          />
        </div>

        <!-- Capability Filter -->
        <div v-if="capabilities.length > 1" class="sm:w-48">
          <BaseSelect
            v-model="selectedCapability"
            icon="ph:funnel"
            shape="curved"
            :disabled="disabled"
          >
            <option value="">All Capabilities</option>
            <option
              v-for="capability in capabilities"
              :key="capability"
              :value="capability"
            >
              {{ formatCapability(capability) }}
            </option>
          </BaseSelect>
        </div>

        <!-- Sort Options -->
        <div class="sm:w-40">
          <BaseSelect
            v-model="sortBy"
            icon="ph:sort-ascending"
            shape="curved"
            :disabled="disabled"
          >
            <option value="name">Name</option>
            <option value="context">Context</option>
            <option value="price">Price</option>
            <option value="date">Date</option>
          </BaseSelect>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex items-center justify-center py-8">
        <div class="flex items-center gap-3">
          <Icon name="ph:spinner" class="size-5 animate-spin text-primary-500" />
          <BaseText class="text-muted-600 dark:text-muted-400">
            Fetching latest models...
          </BaseText>
        </div>
      </div>

      <!-- Error State -->
      <BaseAlert v-else-if="displayError" color="warning" icon="ph:warning">
        {{ displayError }}
      </BaseAlert>

      <!-- Models List -->
      <div v-else-if="sortedModels.length > 0" class="space-y-2">
        <div
          v-for="model in sortedModels"
          :key="model.id"
          class="relative border rounded-lg transition-all duration-200 cursor-pointer"
          :class="[
            modelValue === model.id
              ? 'border-primary-500 bg-primary-50 dark:bg-primary-950/20'
              : 'border-muted-200 dark:border-muted-800 hover:border-muted-300 dark:hover:border-muted-700',
            disabled ? 'opacity-50 cursor-not-allowed' : '',
            model.deprecated ? 'opacity-75' : '',
          ]"
          @click="selectModel(model)"
        >
          <div class="p-4">
            <div class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <!-- Model Name and Status -->
                <div class="flex items-center gap-2 mb-1">
                  <BaseHeading as="h4" size="sm" weight="medium" class="truncate">
                    {{ model.displayName }}
                  </BaseHeading>

                  <!-- Status Badges -->
                  <div class="flex gap-1">
                    <BaseBadge
                      v-if="model.deprecated"
                      color="warning"
                      size="sm"
                    >
                      Deprecated
                    </BaseBadge>

                    <BaseBadge
                      v-if="modelValue === model.id"
                      color="primary"
                      size="sm"
                    >
                      Selected
                    </BaseBadge>
                  </div>
                </div>

                <!-- Model ID -->
                <BaseText size="xs" class="text-muted-500 dark:text-muted-400 mb-2">
                  {{ model.id }}
                </BaseText>

                <!-- Model Details -->
                <div v-if="showDetails" class="flex flex-wrap gap-4 text-xs mb-2">
                  <div class="flex items-center gap-1">
                    <Icon name="ph:text-columns" class="size-3" />
                    <span>{{ formatContextWindow(model.contextWindow) }}</span>
                  </div>

                  <div v-if="model.releaseDate" class="flex items-center gap-1">
                    <Icon name="ph:calendar" class="size-3" />
                    <span>{{ model.releaseDate }}</span>
                  </div>

                  <div v-if="showPricing && model.pricing" class="flex items-center gap-1">
                    <Icon name="ph:currency-dollar" class="size-3" />
                    <span>{{ formatPricing(model) }}</span>
                  </div>
                </div>

                <!-- Capabilities -->
                <div v-if="showDetails && model.capabilities.length > 0" class="flex flex-wrap gap-1">
                  <BaseBadge
                    v-for="capability in model.capabilities"
                    :key="capability"
                    size="sm"
                    variant="soft"
                    class="text-xs"
                  >
                    {{ formatCapability(capability) }}
                  </BaseBadge>
                </div>
              </div>

              <!-- Actions -->
              <div class="flex items-center gap-2 ml-4">
                <!-- Comparison Checkbox -->
                <BaseCheckbox
                  v-if="enableComparison"
                  :model-value="comparedModels.has(model.id)"
                  @update:model-value="toggleComparison(model)"
                  @click.stop
                  size="sm"
                />

                <!-- Selection Radio -->
                <div
                  class="size-4 rounded-full border-2 transition-colors"
                  :class="[
                    modelValue === model.id
                      ? 'border-primary-500 bg-primary-500'
                      : 'border-muted-300 dark:border-muted-600',
                  ]"
                >
                  <div
                    v-if="modelValue === model.id"
                    class="size-full rounded-full bg-white scale-50"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-8">
        <Icon name="ph:robot" class="size-12 mx-auto mb-3 text-muted-400" />
        <BaseHeading as="h4" size="md" weight="medium" class="mb-2">
          No Models Found
        </BaseHeading>
        <BaseText class="text-muted-600 dark:text-muted-400">
          {{ searchQuery ? 'Try adjusting your search or filters.' : 'No models available for this provider.' }}
        </BaseText>
      </div>
    </div>
  </div>
</template>
