<script setup lang="ts">
import type { Integration, LLMProvider } from '~/layers/auth-module/types/integration'

interface Props {
  integrations: Integration[]
  loading?: boolean
  groupByProvider?: boolean
  showInheritance?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  groupByProvider: true,
  showInheritance: false,
})

const emit = defineEmits<{
  add: [provider?: LLMProvider]
  edit: [integration: Integration]
  delete: [integration: Integration]
  toggle: [integration: Integration, value: boolean]
  setDefault: [integration: Integration]
}>()

// Get provider helpers
const { providers, getProviderName, getProviderIcon } = useLLMProviders()
const { getInheritanceStatus } = useIntegrationInheritance()

// Group integrations by provider
const groupedIntegrations = computed(() => {
  if (!props.groupByProvider) {
    return [{ provider: null, integrations: props.integrations }]
  }

  const groups = new Map<string, Integration[]>()

  // Initialize groups for all providers
  providers.value.forEach((provider) => {
    groups.set(provider.id, [])
  })

  // Add integrations to their groups
  props.integrations.forEach((integration) => {
    const group = groups.get(integration.provider) || []
    group.push(integration)
    groups.set(integration.provider, group)
  })

  return Array.from(groups.entries()).map(([provider, integrations]) => ({
    provider,
    integrations,
  }))
})

// Handle events
function handleToggle(integration: Integration, value: boolean) {
  emit('toggle', integration, value)
}

function handleEdit(integration: Integration) {
  emit('edit', integration)
}

function handleDelete(integration: Integration) {
  emit('delete', integration)
}

function handleSetDefault(integration: Integration) {
  emit('setDefault', integration)
}
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="loading" class="space-y-4">
      <BasePlaceholderPage
        title="Loading integrations..."
        subtitle="Please wait while we fetch your integrations"
      >
        <template #image>
          <Icon name="svg-spinners:90-ring" class="size-24 text-primary-500" />
        </template>
      </BasePlaceholderPage>
    </div>

    <!-- Empty state -->
    <div v-else-if="integrations.length === 0" class="text-center py-12">
      <BasePlaceholderPage
        title="No integrations yet"
        subtitle="Add your first integration to start using AI models"
      >
        <template #image>
          <Icon name="ph:robot" class="size-24 text-muted-400" />
        </template>
        <template #action>
          <BaseButton
            color="primary"
            shape="curved"
            @click="emit('add')"
          >
            <Icon name="ph:plus" class="size-4" />
            <span>Add Integration</span>
          </BaseButton>
        </template>
      </BasePlaceholderPage>
    </div>

    <!-- Integrations list -->
    <div v-else class="space-y-8">
      <div
        v-for="group in groupedIntegrations"
        :key="group.provider || 'all'"
      >
        <!-- Provider header -->
        <div
          v-if="groupByProvider && group.provider"
          class="mb-4"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="flex size-10 items-center justify-center rounded-lg bg-muted-100 dark:bg-muted-900">
                <Icon
                  :name="getProviderIcon(group.provider)"
                  class="size-6 text-muted-600 dark:text-muted-400"
                />
              </div>
              <div>
                <BaseHeading
                  as="h3"
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-white"
                >
                  {{ getProviderName(group.provider) }}
                </BaseHeading>
                <BaseText
                  size="xs"
                  class="text-muted-500 dark:text-muted-400"
                >
                  {{ group.integrations.length }} integration{{ group.integrations.length !== 1 ? 's' : '' }}
                </BaseText>
              </div>
            </div>

            <BaseButton
              size="sm"
              variant="soft"
              shape="curved"
              @click="emit('add', group.provider as LLMProvider)"
            >
              <Icon name="ph:plus" class="size-4" />
              <span>Add</span>
            </BaseButton>
          </div>
        </div>

        <!-- Integration cards -->
        <div class="space-y-3">
          <TairoIntegrationCard
            v-for="integration in group.integrations"
            :key="integration.id"
            :integration="integration"
            :show-inheritance="showInheritance"
            :inheritance-status="showInheritance ? getInheritanceStatus(integration, integrations) : undefined"
            @toggle="(value) => handleToggle(integration, value)"
            @edit="handleEdit(integration)"
            @delete="handleDelete(integration)"
            @set-default="handleSetDefault(integration)"
          />
        </div>

        <!-- Empty provider state -->
        <div
          v-if="groupByProvider && group.integrations.length === 0"
          class="rounded-lg border-2 border-dashed border-muted-200 dark:border-muted-800 p-6 text-center"
        >
          <Icon
            :name="getProviderIcon(group.provider)"
            class="size-12 text-muted-400 mx-auto mb-3"
          />
          <BaseText
            size="sm"
            class="text-muted-600 dark:text-muted-400"
          >
            No {{ getProviderName(group.provider) }} integrations yet
          </BaseText>
          <BaseButton
            size="sm"
            variant="soft"
            shape="curved"
            class="mt-3"
            @click="emit('add', group.provider as LLMProvider)"
          >
            <Icon name="ph:plus" class="size-4" />
            <span>Add {{ getProviderName(group.provider) }}</span>
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>
